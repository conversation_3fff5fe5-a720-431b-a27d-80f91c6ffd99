"""
首页轮播图管理路由
"""

import os
import uuid
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from sqlalchemy import text
from PIL import Image, ImageOps
from app import db
from app.models_homepage_carousel import HomepageCarousel

# 创建蓝图
homepage_carousel_bp = Blueprint('homepage_carousel', __name__)


@homepage_carousel_bp.route('/admin/carousel')
@login_required
def admin_list():
    """轮播图管理列表页面"""
    try:
        # 使用原始SQL查询，避免时间精度问题
        sql = text("""
            SELECT
                id, title, description, image_path, link_url,
                sort_order, is_active, created_by, created_at, updated_at
            FROM homepage_carousel
            ORDER BY sort_order ASC, created_at DESC
        """)

        result = db.session.execute(sql)
        carousels = []

        for row in result:
            carousels.append({
                'id': row.id,
                'title': row.title,
                'description': row.description,
                'image_path': row.image_path,
                'link_url': row.link_url,
                'sort_order': row.sort_order,
                'is_active': row.is_active,
                'created_at': row.created_at.strftime('%Y-%m-%d %H:%M') if row.created_at else '',
                'updated_at': row.updated_at.strftime('%Y-%m-%d %H:%M') if row.updated_at else ''
            })

        return render_template('admin/carousel_list.html', carousels=carousels)

    except Exception as e:
        current_app.logger.error(f"获取轮播图列表失败: {str(e)}")
        flash('获取轮播图列表失败，请检查数据库连接', 'danger')
        carousels = []

    return render_template('admin/carousel_list.html',
                         carousels=carousels,
                         title='轮播图管理')


@homepage_carousel_bp.route('/admin/carousel/create', methods=['GET', 'POST'])
@login_required
def admin_create():
    """创建轮播图"""
    if request.method == 'POST':
        try:
            title = request.form.get('title', '').strip()
            description = request.form.get('description', '').strip()
            link_url = request.form.get('link_url', '').strip()
            is_active = request.form.get('is_active') == '1'

            # 验证必填字段
            if not title:
                flash('请输入图片标题', 'danger')
                return render_template('admin/carousel_form.html')

            # 处理多文件上传
            if 'images' not in request.files:
                flash('请选择图片文件', 'danger')
                return render_template('admin/carousel_form.html')

            files = request.files.getlist('images')
            if not files or all(f.filename == '' for f in files):
                flash('请选择图片文件', 'danger')
                return render_template('admin/carousel_form.html')

            # 获取图片标题列表
            image_titles = request.form.getlist('image_titles[]')

            # 获取下一个排序号
            max_order_result = db.session.execute(text("SELECT MAX(sort_order) FROM homepage_carousel")).scalar()
            next_order = (max_order_result or 0) + 1

            created_count = 0
            failed_files = []

            # 处理每个文件
            for index, file in enumerate(files):
                if file.filename == '':
                    continue

                if not HomepageCarousel.allowed_file(file.filename):
                    failed_files.append(f"{file.filename} (不支持的文件类型)")
                    continue

                # 保存文件
                image_path = save_uploaded_file(file)
                if not image_path:
                    failed_files.append(f"{file.filename} (上传失败)")
                    continue

                # 获取对应的标题，如果没有则使用默认标题
                image_title = image_titles[index] if index < len(image_titles) and image_titles[index].strip() else title
                if not image_title.strip():
                    image_title = f"轮播图 {index + 1}"

                # 使用原始SQL插入数据
                sql = text("""
                    INSERT INTO homepage_carousel
                    (title, description, image_path, link_url, sort_order, is_active, created_by)
                    VALUES
                    (:title, :description, :image_path, :link_url, :sort_order, :is_active, :created_by)
                """)

                try:
                    db.session.execute(sql, {
                        'title': image_title,
                        'description': description,
                        'image_path': image_path,
                        'link_url': link_url if link_url else '/',  # 默认跳转首页
                        'sort_order': next_order + index,
                        'is_active': is_active,
                        'created_by': current_user.id
                    })
                    created_count += 1
                except Exception as e:
                    failed_files.append(f"{file.filename} (数据库错误)")
                    current_app.logger.error(f"插入轮播图数据失败: {str(e)}")

            db.session.commit()

            # 显示结果消息
            if created_count > 0:
                flash(f'成功创建 {created_count} 个轮播图（已自动压缩为1920x800尺寸）', 'success')

            if failed_files:
                flash(f'以下文件处理失败: {", ".join(failed_files)}', 'warning')

            if created_count == 0:
                flash('没有成功创建任何轮播图', 'danger')
                return render_template('admin/carousel_form.html')

            return redirect(url_for('homepage_carousel.admin_list'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建轮播图失败: {str(e)}")
            flash('创建轮播图失败', 'danger')

    return render_template('admin/carousel_form.html')


@homepage_carousel_bp.route('/admin/carousel/batch-upload', methods=['GET', 'POST'])
@login_required
def admin_batch_upload():
    """批量上传轮播图"""
    if request.method == 'POST':
        try:
            default_description = request.form.get('default_description', '').strip()
            default_link_url = request.form.get('default_link_url', '/').strip()
            is_active = request.form.get('is_active') == '1'

            # 处理多文件上传
            if 'images' not in request.files:
                flash('请选择图片文件', 'danger')
                return render_template('admin/carousel_batch_upload.html')

            files = request.files.getlist('images')
            if not files or all(f.filename == '' for f in files):
                flash('请选择图片文件', 'danger')
                return render_template('admin/carousel_batch_upload.html')

            # 获取图片标题列表
            image_titles = request.form.getlist('image_titles[]')

            # 获取下一个排序号
            max_order_result = db.session.execute(text("SELECT MAX(sort_order) FROM homepage_carousel")).scalar()
            next_order = (max_order_result or 0) + 1

            created_count = 0
            failed_files = []

            # 处理每个文件
            for index, file in enumerate(files):
                if file.filename == '':
                    continue

                if not HomepageCarousel.allowed_file(file.filename):
                    failed_files.append(f"{file.filename} (不支持的文件类型)")
                    continue

                # 保存文件
                image_path = save_uploaded_file(file)
                if not image_path:
                    failed_files.append(f"{file.filename} (上传失败)")
                    continue

                # 获取对应的标题，如果没有则使用默认标题
                image_title = image_titles[index] if index < len(image_titles) and image_titles[index].strip() else f"轮播图 {index + 1}"

                # 使用原始SQL插入数据
                sql = text("""
                    INSERT INTO homepage_carousel
                    (title, description, image_path, link_url, sort_order, is_active, created_by)
                    VALUES
                    (:title, :description, :image_path, :link_url, :sort_order, :is_active, :created_by)
                """)

                try:
                    db.session.execute(sql, {
                        'title': image_title,
                        'description': default_description,
                        'image_path': image_path,
                        'link_url': default_link_url,
                        'sort_order': next_order + index,
                        'is_active': is_active,
                        'created_by': current_user.id
                    })
                    created_count += 1
                except Exception as e:
                    failed_files.append(f"{file.filename} (数据库错误)")
                    current_app.logger.error(f"插入轮播图数据失败: {str(e)}")

            db.session.commit()

            # 显示结果消息
            if created_count > 0:
                flash(f'批量上传成功！共创建 {created_count} 个轮播图（已自动压缩为1920x800尺寸）', 'success')

            if failed_files:
                flash(f'以下文件处理失败: {", ".join(failed_files)}', 'warning')

            if created_count == 0:
                flash('没有成功创建任何轮播图', 'danger')
                return render_template('admin/carousel_batch_upload.html')

            return redirect(url_for('homepage_carousel.admin_list'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量上传轮播图失败: {str(e)}")
            flash('批量上传失败', 'danger')

    return render_template('admin/carousel_batch_upload.html')


@homepage_carousel_bp.route('/admin/carousel/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def admin_edit(id):
    """编辑轮播图"""
    try:
        # 获取轮播图数据
        sql = text("SELECT * FROM homepage_carousel WHERE id = :id")
        result = db.session.execute(sql, {'id': id}).fetchone()

        if not result:
            flash('轮播图不存在', 'danger')
            return redirect(url_for('homepage_carousel.admin_list'))

        carousel = {
            'id': result.id,
            'title': result.title,
            'description': result.description,
            'image_path': result.image_path,
            'link_url': result.link_url,
            'sort_order': result.sort_order,
            'is_active': result.is_active
        }

        if request.method == 'POST':
            title = request.form.get('title', '').strip()
            description = request.form.get('description', '').strip()
            link_url = request.form.get('link_url', '').strip()
            is_active = request.form.get('is_active') == '1'

            if not title:
                flash('请输入图片标题', 'danger')
                return render_template('admin/carousel_form.html', carousel=carousel)

            # 处理图片更新
            image_path = carousel['image_path']  # 保持原图片
            if 'images' in request.files and request.files['images'].filename != '':
                file = request.files['images']
                if HomepageCarousel.allowed_file(file.filename):
                    # 删除旧图片
                    delete_image_file(carousel['image_path'])
                    # 保存新图片
                    new_image_path = save_uploaded_file(file)
                    if new_image_path:
                        image_path = new_image_path
                else:
                    flash('不支持的文件类型', 'danger')
                    return render_template('admin/carousel_form.html', carousel=carousel)

            # 更新数据
            update_sql = text("""
                UPDATE homepage_carousel
                SET title = :title, description = :description, image_path = :image_path,
                    link_url = :link_url, is_active = :is_active
                WHERE id = :id
            """)

            db.session.execute(update_sql, {
                'title': title,
                'description': description,
                'image_path': image_path,
                'link_url': link_url if link_url else '/',
                'is_active': is_active,
                'id': id
            })

            db.session.commit()
            flash('轮播图更新成功', 'success')
            return redirect(url_for('homepage_carousel.admin_list'))

        return render_template('admin/carousel_form.html', carousel=carousel)

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"编辑轮播图失败: {str(e)}")
        flash('编辑轮播图失败', 'danger')
        return redirect(url_for('homepage_carousel.admin_list'))


@homepage_carousel_bp.route('/admin/carousel/<int:id>/delete', methods=['POST'])
@login_required
def admin_delete(id):
    """删除轮播图"""
    try:
        # 获取轮播图信息
        sql = text("SELECT image_path FROM homepage_carousel WHERE id = :id")
        result = db.session.execute(sql, {'id': id}).fetchone()

        if result:
            # 删除图片文件
            delete_image_file(result.image_path)

            # 删除数据库记录
            delete_sql = text("DELETE FROM homepage_carousel WHERE id = :id")
            db.session.execute(delete_sql, {'id': id})
            db.session.commit()

            flash('轮播图删除成功', 'success')
        else:
            flash('轮播图不存在', 'danger')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除轮播图失败: {str(e)}")
        flash('删除轮播图失败', 'danger')

    return redirect(url_for('homepage_carousel.admin_list'))


@homepage_carousel_bp.route('/admin/carousel/<int:id>/toggle', methods=['POST'])
@login_required
def admin_toggle(id):
    """切换轮播图启用状态"""
    try:
        sql = text("UPDATE homepage_carousel SET is_active = ~is_active WHERE id = :id")
        db.session.execute(sql, {'id': id})
        db.session.commit()

        return jsonify({'success': True, 'message': '状态更新成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"切换轮播图状态失败: {str(e)}")
        return jsonify({'success': False, 'message': '状态更新失败'})


@homepage_carousel_bp.route('/admin/carousel/<int:id>/sort', methods=['POST'])
@login_required
def admin_sort(id):
    """调整轮播图排序"""
    try:
        direction = request.json.get('direction')  # 'up' 或 'down'

        if direction not in ['up', 'down']:
            return jsonify({'success': False, 'message': '无效的排序方向'})

        # 获取当前轮播图的排序值
        current_sql = text("SELECT sort_order FROM homepage_carousel WHERE id = :id")
        current_result = db.session.execute(current_sql, {'id': id}).fetchone()

        if not current_result:
            return jsonify({'success': False, 'message': '轮播图不存在'})

        current_order = current_result.sort_order

        if direction == 'up':
            # 向上移动：找到前一个更小的排序值
            target_sql = text("""
                SELECT id, sort_order FROM homepage_carousel
                WHERE sort_order < :current_order
                ORDER BY sort_order DESC
                LIMIT 1
            """)
        else:
            # 向下移动：找到后一个更大的排序值
            target_sql = text("""
                SELECT id, sort_order FROM homepage_carousel
                WHERE sort_order > :current_order
                ORDER BY sort_order ASC
                LIMIT 1
            """)

        target_result = db.session.execute(target_sql, {'current_order': current_order}).fetchone()

        if not target_result:
            return jsonify({'success': False, 'message': '已经是最' + ('前' if direction == 'up' else '后') + '了'})

        target_id = target_result.id
        target_order = target_result.sort_order

        # 交换排序值
        update_current_sql = text("UPDATE homepage_carousel SET sort_order = :new_order WHERE id = :id")
        update_target_sql = text("UPDATE homepage_carousel SET sort_order = :new_order WHERE id = :id")

        db.session.execute(update_current_sql, {'new_order': target_order, 'id': id})
        db.session.execute(update_target_sql, {'new_order': current_order, 'id': target_id})

        db.session.commit()

        return jsonify({'success': True, 'message': '排序调整成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"调整排序失败: {str(e)}")
        return jsonify({'success': False, 'message': '排序调整失败'})


@homepage_carousel_bp.route('/admin/carousel/batch-delete', methods=['POST'])
@login_required
def admin_batch_delete():
    """批量删除轮播图"""
    try:
        ids = request.json.get('ids', [])

        if not ids:
            return jsonify({'success': False, 'message': '请选择要删除的轮播图'})

        deleted_count = 0

        for carousel_id in ids:
            # 获取图片路径
            sql = text("SELECT image_path FROM homepage_carousel WHERE id = :id")
            result = db.session.execute(sql, {'id': carousel_id}).fetchone()

            if result:
                # 删除图片文件
                delete_image_file(result.image_path)

                # 删除数据库记录
                delete_sql = text("DELETE FROM homepage_carousel WHERE id = :id")
                db.session.execute(delete_sql, {'id': carousel_id})
                deleted_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 个轮播图'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量删除失败: {str(e)}")
        return jsonify({'success': False, 'message': '批量删除失败'})


def save_uploaded_file(file):
    """保存上传的文件并进行压缩处理"""
    try:
        # 创建上传目录
        upload_folder = 'uploads/carousel'
        upload_path = os.path.join(current_app.static_folder, upload_folder)
        os.makedirs(upload_path, exist_ok=True)

        # 生成唯一文件名（统一使用.jpg扩展名）
        unique_filename = f"{uuid.uuid4().hex}.jpg"
        file_path = os.path.join(upload_path, unique_filename)

        # 打开并处理图片
        with Image.open(file) as img:
            # 转换为RGB模式（处理RGBA、P等模式）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # 轮播图推荐尺寸：1920x800（适合桌面端轮播）
            # 保持宽高比，最大宽度1920px，最大高度800px
            target_size = (1920, 800)

            # 计算缩放比例，保持宽高比
            img_ratio = img.width / img.height
            target_ratio = target_size[0] / target_size[1]

            if img_ratio > target_ratio:
                # 图片更宽，以宽度为准
                new_width = target_size[0]
                new_height = int(target_size[0] / img_ratio)
            else:
                # 图片更高，以高度为准
                new_height = target_size[1]
                new_width = int(target_size[1] * img_ratio)

            # 调整图片大小
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 保存压缩后的图片，质量85%
            img.save(file_path, 'JPEG', quality=85, optimize=True)

        # 返回相对路径
        return f"/static/{upload_folder}/{unique_filename}"

    except Exception as e:
        current_app.logger.error(f"保存文件失败: {str(e)}")
        return None


def delete_image_file(image_path):
    """删除图片文件"""
    try:
        if image_path and image_path.startswith('/static/'):
            file_path = os.path.join(current_app.static_folder, image_path[8:])  # 去掉 '/static/'
            if os.path.exists(file_path):
                os.remove(file_path)
                current_app.logger.info(f"删除图片文件: {file_path}")
    except Exception as e:
        current_app.logger.error(f"删除图片文件失败: {str(e)}")


@homepage_carousel_bp.route('/api/carousel/list')
def api_list():
    """API: 获取启用的轮播图列表"""
    carousels = []
    try:
        sql = text("""
            SELECT id, title, description, image_path, link_url, sort_order
            FROM homepage_carousel
            WHERE is_active = 1
            ORDER BY sort_order ASC, created_at DESC
        """)

        result = db.session.execute(sql)

        for row in result:
            carousels.append({
                'id': row.id,
                'title': row.title,
                'description': row.description,
                'image_path': row.image_path,
                'link_url': row.link_url,
                'sort_order': row.sort_order
            })

    except Exception as e:
        current_app.logger.error(f"获取轮播图API失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取轮播图失败'
        })
    finally:
        # 确保数据库会话正确关闭
        try:
            db.session.close()
        except:
            pass

    return jsonify({
        'success': True,
        'data': carousels,
        'count': len(carousels)
    })
