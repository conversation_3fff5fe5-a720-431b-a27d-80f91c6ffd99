#!/usr/bin/env python3
"""
按照README.md最佳实践修复在线咨询功能
使用DATETIME2(precision=1)和数据库默认值
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_consultation_final():
    """按照最佳实践修复咨询功能"""
    try:
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            print("🔧 按照README.md最佳实践修复在线咨询功能...")
            print("=" * 60)
            
            # 删除旧表
            try:
                print("🗑️ 删除旧表...")
                db.session.execute(text("DROP TRIGGER IF EXISTS TR_online_consultations_update"))
                db.session.execute(text("DROP TABLE IF EXISTS online_consultations"))
                db.session.commit()
                print("✓ 旧表已删除")
            except Exception as e:
                print(f"删除旧表时出错（可能不存在）: {e}")
                db.session.rollback()
            
            # 按照README.md最佳实践创建表
            print("📝 创建新表（按照README.md最佳实践）...")
            create_sql = text("""
            CREATE TABLE online_consultations (
                id INT IDENTITY(1,1) PRIMARY KEY,
                name NVARCHAR(50) NOT NULL,
                contact_type NVARCHAR(20) NOT NULL DEFAULT '微信',
                contact_value NVARCHAR(100) NOT NULL,
                content NVARCHAR(MAX) NOT NULL,
                status NVARCHAR(20) NOT NULL DEFAULT '待处理',
                reply_content NVARCHAR(MAX) NULL,
                reply_time DATETIME2(1) NULL,
                reply_user_id INT NULL,
                source NVARCHAR(50) NOT NULL DEFAULT '官网首页',
                ip_address NVARCHAR(50) NULL,
                user_agent NVARCHAR(500) NULL,
                created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
                updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE()
            );
            """)
            
            db.session.execute(create_sql)
            db.session.commit()
            print("✓ 新表创建成功（DATETIME2(1)精度，使用数据库默认值）")
            
            # 创建索引
            print("📊 创建索引...")
            indexes = [
                "CREATE INDEX IX_online_consultations_status ON online_consultations(status);",
                "CREATE INDEX IX_online_consultations_created_at ON online_consultations(created_at);",
                "CREATE INDEX IX_online_consultations_contact_type ON online_consultations(contact_type);"
            ]
            
            for idx_sql in indexes:
                db.session.execute(text(idx_sql))
            
            db.session.commit()
            print("✓ 索引创建成功")
            
            # 创建更新触发器
            print("⚡ 创建更新触发器...")
            trigger_sql = text("""
            CREATE TRIGGER TR_online_consultations_update
            ON online_consultations
            AFTER UPDATE
            AS
            BEGIN
                SET NOCOUNT ON;
                UPDATE online_consultations 
                SET updated_at = GETDATE()
                FROM online_consultations oc
                INNER JOIN inserted i ON oc.id = i.id;
            END;
            """)
            
            db.session.execute(trigger_sql)
            db.session.commit()
            print("✓ 更新触发器创建成功")
            
            # 测试插入（按照最佳实践：不手动设置时间字段）
            print("🧪 测试插入功能...")
            test_sql = text("""
                INSERT INTO online_consultations 
                (name, contact_type, contact_value, content)
                OUTPUT inserted.id, inserted.created_at, inserted.updated_at
                VALUES 
                ('测试用户', '微信', 'test123', '这是按照README.md最佳实践的测试')
            """)
            
            result = db.session.execute(test_sql)
            test_record = result.fetchone()
            db.session.commit()
            
            print(f"✓ 测试插入成功")
            print(f"  ID: {test_record[0]}")
            print(f"  创建时间: {test_record[1]}")
            print(f"  更新时间: {test_record[2]}")
            
            # 测试更新（按照最佳实践：不手动设置updated_at）
            print("🔄 测试更新功能...")
            update_sql = text("""
                UPDATE online_consultations 
                SET content = '更新后的内容'
                WHERE id = :id
            """)
            
            db.session.execute(update_sql, {'id': test_record[0]})
            db.session.commit()
            
            # 检查更新后的时间
            check_sql = text("""
                SELECT created_at, updated_at 
                FROM online_consultations 
                WHERE id = :id
            """)
            
            result = db.session.execute(check_sql, {'id': test_record[0]})
            updated_record = result.fetchone()
            
            print(f"✓ 测试更新成功")
            print(f"  创建时间: {updated_record[0]} (未变)")
            print(f"  更新时间: {updated_record[1]} (已自动更新)")
            
            # 删除测试数据
            db.session.execute(text("DELETE FROM online_consultations WHERE id = :id"), {'id': test_record[0]})
            db.session.commit()
            print("✓ 测试数据已清理")
            
            # 插入真实测试数据
            print("📋 插入测试数据...")
            test_data = [
                ('张三', '微信', 'zhangsan123', '想了解一下智慧食堂管理系统的具体功能和价格'),
                ('李四', '电话', '13800138000', '我们学校想试用这个系统，请联系我'),
                ('王五', '微信', 'wangwu888', '系统支持多少个学校同时使用？'),
                ('赵六', '电话', '13900139000', '能否提供现场演示？我们是县教育局的')
            ]
            
            for data in test_data:
                insert_sql = text("""
                    INSERT INTO online_consultations (name, contact_type, contact_value, content)
                    VALUES (:name, :contact_type, :contact_value, :content)
                """)
                
                db.session.execute(insert_sql, {
                    'name': data[0],
                    'contact_type': data[1],
                    'contact_value': data[2],
                    'content': data[3]
                })
            
            db.session.commit()
            print(f"✓ 插入了 {len(test_data)} 条测试数据")
            
            # 验证表结构
            print("🔍 验证表结构...")
            result = db.session.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, DATETIME_PRECISION, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'online_consultations' 
                ORDER BY ORDINAL_POSITION
            """))
            
            print("\n表结构验证：")
            print("-" * 80)
            print(f"{'字段名':<20} {'数据类型':<15} {'精度':<8} {'默认值':<20}")
            print("-" * 80)
            
            for row in result:
                column_name = row[0]
                data_type = row[1]
                precision = row[2] if row[2] is not None else ''
                default_value = row[3] if row[3] else ''
                print(f"{column_name:<20} {data_type:<15} {str(precision):<8} {str(default_value):<20}")
            
            print("-" * 80)
            
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 在线咨询功能最终修复")
    print("=" * 60)
    print("按照README.md中的最佳实践：")
    print("1. 使用DATETIME2(precision=1)精确到0.1秒")
    print("2. 不手动设置时间字段，让数据库使用默认值")
    print("3. 使用参数化查询，避免时间精度问题")
    print("4. 创建触发器自动处理updated_at")
    print("=" * 60)
    
    confirm = input("确认执行最终修复？(y/n): ").lower().strip()
    
    if confirm == 'y':
        if fix_consultation_final():
            print("\n✅ 最终修复成功！")
            print("\n📋 修复完成，现在：")
            print("1. 重启应用服务器")
            print("2. 测试咨询表单提交")
            print("3. 应该不再有精度错误")
            print("\n🎯 关键改进：")
            print("- 使用DATETIME2(1)精度")
            print("- 数据库自动处理时间字段")
            print("- API不再手动设置时间")
            print("- 触发器自动更新updated_at")
        else:
            print("\n❌ 修复失败！请检查错误信息")
            sys.exit(1)
    else:
        print("❌ 操作已取消")
        sys.exit(0)

if __name__ == '__main__':
    main()
