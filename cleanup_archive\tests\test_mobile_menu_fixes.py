#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
移动端菜单修复和食材整合测试脚本
验证移动端下拉菜单交互和食材功能整合是否正确
"""

import os
import sys

def test_mobile_css_fixes():
    """测试移动端CSS修复"""
    print("📱 测试移动端CSS修复...")
    
    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # 检查移动端交互修复
        mobile_fixes = [
            ('cursor: pointer', '指针样式'),
            ('-webkit-tap-highlight-color', '触摸高亮'),
            ('touch-action: manipulation', '触摸操作'),
            ('user-select: none', '禁用文本选择'),
            ('text-decoration: none', '移除文本装饰'),
            ('width: 100% !important', '移动端全宽'),
            ('display: block', '块级显示'),
            ('-webkit-overflow-scrolling: touch', '触摸滚动')
        ]
        
        results = []
        for fix, description in mobile_fixes:
            if fix in css_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 移动端CSS测试失败: {str(e)}")
        return False

def test_ingredient_integration():
    """测试食材功能整合"""
    print("\n🥕 测试食材功能整合...")
    
    try:
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        # 查找菜单规划模块
        menu_planning = None
        for item in ELEGANT_MENU_CONFIG:
            if item['id'] == 'menu_planning':
                menu_planning = item
                break
        
        if not menu_planning:
            print("❌ 未找到菜单规划模块")
            return False
        
        print(f"✅ 找到菜单规划模块: {menu_planning['name']}")
        
        # 检查食材相关功能
        ingredient_items = []
        recipe_items = []
        
        for child in menu_planning['children']:
            if 'ingredient' in child.get('id', '').lower():
                ingredient_items.append(child)
            elif 'recipe' in child.get('id', '').lower():
                recipe_items.append(child)
        
        print(f"  📚 食谱相关项目: {len(recipe_items)}个")
        for item in recipe_items:
            if not item.get('is_header'):
                print(f"    - {item['name']}")
        
        print(f"  🥕 食材相关项目: {len(ingredient_items)}个")
        for item in ingredient_items:
            if not item.get('is_header'):
                print(f"    - {item['name']}")
        
        # 检查分组标题
        headers = [child for child in menu_planning['children'] if child.get('is_header')]
        print(f"  🏷️ 分组标题: {len(headers)}个")
        for header in headers:
            print(f"    - {header['name']}")
        
        # 验证必要的食材功能是否存在
        expected_items = ['食谱库', '食材管理', '食材分类']
        found_items = []
        
        for child in menu_planning['children']:
            if not child.get('is_header') and child['name'] in expected_items:
                found_items.append(child['name'])
        
        missing_items = set(expected_items) - set(found_items)
        
        if missing_items:
            print(f"  ❌ 缺少功能: {', '.join(missing_items)}")
            return False
        else:
            print("  ✅ 所有必要的食材功能都已整合")
            return True
        
    except Exception as e:
        print(f"❌ 食材功能整合测试失败: {str(e)}")
        return False

def test_menu_structure_optimization():
    """测试菜单结构优化"""
    print("\n📊 测试菜单结构优化...")
    
    try:
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        # 统计菜单规划模块的子项
        menu_planning = None
        for item in ELEGANT_MENU_CONFIG:
            if item['id'] == 'menu_planning':
                menu_planning = item
                break
        
        if not menu_planning:
            return False
        
        total_children = len(menu_planning['children'])
        headers = len([child for child in menu_planning['children'] if child.get('is_header')])
        functional_items = total_children - headers
        
        print(f"  📋 总子项: {total_children}个")
        print(f"  🏷️ 分组标题: {headers}个")
        print(f"  ⚙️ 功能项目: {functional_items}个")
        
        # 检查逻辑分组
        groups = {}
        current_group = None
        
        for child in menu_planning['children']:
            if child.get('is_header'):
                current_group = child['name']
                groups[current_group] = []
            elif current_group:
                groups[current_group].append(child['name'])
        
        print(f"  📂 逻辑分组:")
        for group_name, items in groups.items():
            print(f"    {group_name}: {len(items)}个功能")
            for item in items:
                print(f"      - {item}")
        
        # 验证分组合理性
        if len(groups) >= 2 and functional_items >= 6:
            print("  ✅ 菜单结构优化合理")
            return True
        else:
            print("  ⚠️ 菜单结构可能需要进一步优化")
            return False
        
    except Exception as e:
        print(f"❌ 菜单结构测试失败: {str(e)}")
        return False

def test_route_validity():
    """测试新增路由有效性"""
    print("\n🛣️ 测试新增路由有效性...")
    
    try:
        from app import create_app
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        
        app = create_app()
        available_endpoints = {rule.endpoint for rule in app.url_map.iter_rules()}
        
        # 检查食材相关路由
        ingredient_routes = [
            'ingredient.index',
            'ingredient_category.index'
        ]
        
        results = []
        for route in ingredient_routes:
            if route in available_endpoints:
                print(f"  ✅ {route}")
                results.append(True)
            else:
                print(f"  ❌ {route} - 路由不存在")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 路由有效性测试失败: {str(e)}")
        return False

def generate_integration_summary():
    """生成整合总结"""
    print("\n📋 移动端修复和食材整合总结:")
    print("-" * 50)
    
    mobile_fixes = [
        "📱 修复移动端下拉菜单无法点击的问题",
        "👆 添加触摸优化和点击反馈",
        "🎯 改善移动端用户交互体验",
        "📐 确保移动端菜单项全宽显示"
    ]
    
    integration_improvements = [
        "🥕 将食材管理整合到菜单规划模块",
        "📚 食谱与食材功能逻辑分组",
        "🏷️ 添加清晰的分组标题",
        "🎯 提高功能查找效率"
    ]
    
    print("  移动端修复:")
    for fix in mobile_fixes:
        print(f"    {fix}")
    
    print("\n  食材功能整合:")
    for improvement in integration_improvements:
        print(f"    {improvement}")
    
    print("\n💡 整合效果:")
    print("  • 移动端用户可以正常使用下拉菜单")
    print("  • 食材相关功能集中在菜单规划中")
    print("  • 菜单逻辑更加清晰和直观")
    print("  • 减少用户在不同模块间跳转")

def main():
    """主测试函数"""
    print("🔍 开始测试移动端修复和食材整合...")
    print("=" * 60)
    
    test_results = []
    
    # 测试移动端CSS修复
    test_results.append(test_mobile_css_fixes())
    
    # 测试食材功能整合
    test_results.append(test_ingredient_integration())
    
    # 测试菜单结构优化
    test_results.append(test_menu_structure_optimization())
    
    # 测试路由有效性
    test_results.append(test_route_validity())
    
    # 统计结果
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 移动端修复和食材整合成功！")
        generate_integration_summary()
        
        print("\n🚀 现在移动端用户可以正常使用菜单，食材功能也更好地整合了！")
        print("📱 请在移动设备上测试下拉菜单功能")
        return True
    else:
        print("⚠️ 部分功能未完成，请检查相关配置。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
