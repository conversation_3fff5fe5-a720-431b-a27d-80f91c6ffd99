#!/usr/bin/env python3
"""
修复关键 CSP 问题脚本
专门修复重大和严重影响的 CSP 错误
"""

import os
import re
import glob
from pathlib import Path

def fix_critical_issues():
    """修复关键 CSP 问题"""
    print("🚨 修复关键 CSP 问题")
    print("=" * 60)
    
    # 统计数据
    stats = {
        'files_processed': 0,
        'critical_fixes': 0,
        'major_fixes': 0,
        'backup_created': 0
    }
    
    # 查找所有 HTML 文件
    html_files = glob.glob('app/templates/**/*.html', recursive=True)
    
    print(f"🔍 扫描 {len(html_files)} 个文件...")
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = fix_file_critical_issues(file_path, content)
            
            if file_fixes['modified']:
                # 创建备份
                backup_path = file_path + '.critical.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 保存修复后的文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(file_fixes['content'])
                
                stats['files_processed'] += 1
                stats['critical_fixes'] += file_fixes['critical_count']
                stats['major_fixes'] += file_fixes['major_count']
                stats['backup_created'] += 1
                
                print(f"✅ {file_path}")
                print(f"   修复关键问题: {file_fixes['critical_count']} 个")
                print(f"   修复重大问题: {file_fixes['major_count']} 个")
                print(f"   备份已创建: {backup_path}")
                
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path}: {e}")
    
    # 生成修复报告
    generate_fix_report(stats)
    
    return stats

def fix_file_critical_issues(file_path, content):
    """修复单个文件的关键问题"""
    result = {
        'content': content,
        'modified': False,
        'critical_count': 0,
        'major_count': 0
    }
    
    # 1. 修复危险的删除操作 onclick
    delete_patterns = [
        # 删除确认模式
        (r'onclick\s*=\s*["\']return\s+confirm\s*\([^)]*\)\s*&&\s*[^"\']*["\']', 'delete_confirm'),
        (r'onclick\s*=\s*["\']if\s*\(\s*confirm\s*\([^)]*\)\s*\)[^"\']*["\']', 'delete_confirm'),
        (r'onclick\s*=\s*["\'][^"\']*confirm\s*\([^)]*delete[^)]*\)[^"\']*["\']', 'delete_confirm'),
        (r'onclick\s*=\s*["\'][^"\']*delete[^"\']*confirm[^"\']*["\']', 'delete_confirm'),
        
        # 直接删除模式
        (r'onclick\s*=\s*["\'][^"\']*delete[^"\']*\([^)]*\)[^"\']*["\']', 'delete_direct'),
        (r'onclick\s*=\s*["\'][^"\']*remove[^"\']*\([^)]*\)[^"\']*["\']', 'delete_direct'),
    ]
    
    for pattern, action_type in delete_patterns:
        matches = list(re.finditer(pattern, result['content'], re.IGNORECASE))
        for match in matches:
            original_onclick = match.group(0)
            
            # 提取函数调用
            onclick_content = re.search(r'["\']([^"\']*)["\']', original_onclick).group(1)
            
            # 生成安全的替代方案
            if action_type == 'delete_confirm':
                # 保留确认逻辑的删除操作
                replacement = f'data-action="delete-with-confirm" data-confirm-message="确定要删除吗？" data-delete-function="{onclick_content}"'
            else:
                # 直接删除操作，添加确认
                replacement = f'data-action="delete-direct" data-confirm-message="确定要删除这个项目吗？" data-delete-function="{onclick_content}"'
            
            result['content'] = result['content'].replace(original_onclick, replacement)
            result['modified'] = True
            result['major_fixes'] += 1
    
    # 2. 修复关键的表单验证 onsubmit
    submit_patterns = [
        r'onsubmit\s*=\s*["\']return\s+[^"\']*validate[^"\']*["\']',
        r'onsubmit\s*=\s*["\']return\s+[^"\']*check[^"\']*["\']',
        r'onsubmit\s*=\s*["\'][^"\']*return\s+false[^"\']*["\']'
    ]
    
    for pattern in submit_patterns:
        matches = list(re.finditer(pattern, result['content'], re.IGNORECASE))
        for match in matches:
            original_onsubmit = match.group(0)
            
            # 提取验证函数
            submit_content = re.search(r'["\']([^"\']*)["\']', original_onsubmit).group(1)
            
            # 生成替代方案
            replacement = f'data-validation="true" data-submit-handler="{submit_content}"'
            
            result['content'] = result['content'].replace(original_onsubmit, replacement)
            result['modified'] = True
            result['major_fixes'] += 1
    
    # 3. 修复其他关键的安全操作
    security_patterns = [
        (r'onclick\s*=\s*["\'][^"\']*confirm\s*\([^)]*\)[^"\']*["\']', 'confirm_action'),
        (r'onclick\s*=\s*["\']return\s+confirm\s*\([^)]*\)["\']', 'confirm_return'),
    ]
    
    for pattern, action_type in security_patterns:
        matches = list(re.finditer(pattern, result['content'], re.IGNORECASE))
        for match in matches:
            original_onclick = match.group(0)
            
            # 提取确认内容
            onclick_content = re.search(r'["\']([^"\']*)["\']', original_onclick).group(1)
            
            if action_type == 'confirm_action':
                replacement = f'data-action="confirm" data-confirm-handler="{onclick_content}"'
            else:
                replacement = f'data-action="confirm-return" data-confirm-handler="{onclick_content}"'
            
            result['content'] = result['content'].replace(original_onclick, replacement)
            result['modified'] = True
            result['major_fixes'] += 1
    
    return result

def create_critical_event_handler():
    """创建关键事件处理器"""
    print("\n📝 创建关键事件处理器...")
    
    handler_script = '''/**
 * 关键事件处理器
 * 专门处理删除确认、表单验证等关键功能
 */

(function() {
    'use strict';
    
    // 关键事件处理器
    const CriticalEventHandler = {
        
        // 初始化
        init: function() {
            this.bindDeleteActions();
            this.bindFormValidation();
            this.bindConfirmActions();
            console.log('✅ 关键事件处理器已初始化');
        },
        
        // 绑定删除操作
        bindDeleteActions: function() {
            // 带确认的删除操作
            document.querySelectorAll('[data-action="delete-with-confirm"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const confirmMessage = element.getAttribute('data-confirm-message') || '确定要删除吗？';
                    const deleteFunction = element.getAttribute('data-delete-function');
                    
                    if (confirm(confirmMessage)) {
                        try {
                            // 安全执行删除函数
                            this.safeExecute(deleteFunction);
                        } catch (error) {
                            console.error('删除操作执行失败:', error);
                            alert('删除操作失败，请重试');
                        }
                    }
                });
            });
            
            // 直接删除操作（添加确认）
            document.querySelectorAll('[data-action="delete-direct"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const confirmMessage = element.getAttribute('data-confirm-message') || '确定要删除这个项目吗？';
                    const deleteFunction = element.getAttribute('data-delete-function');
                    
                    if (confirm(confirmMessage)) {
                        try {
                            this.safeExecute(deleteFunction);
                        } catch (error) {
                            console.error('删除操作执行失败:', error);
                            alert('删除操作失败，请重试');
                        }
                    }
                });
            });
        },
        
        // 绑定表单验证
        bindFormValidation: function() {
            document.querySelectorAll('[data-validation="true"]').forEach(form => {
                form.addEventListener('submit', (e) => {
                    const submitHandler = form.getAttribute('data-submit-handler');
                    
                    try {
                        // 执行验证函数
                        const result = this.safeExecute(submitHandler);
                        
                        // 如果验证失败，阻止提交
                        if (result === false) {
                            e.preventDefault();
                            console.log('表单验证失败，已阻止提交');
                        }
                    } catch (error) {
                        console.error('表单验证执行失败:', error);
                        e.preventDefault();
                        alert('表单验证失败，请检查输入');
                    }
                });
            });
        },
        
        // 绑定确认操作
        bindConfirmActions: function() {
            // 一般确认操作
            document.querySelectorAll('[data-action="confirm"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const confirmHandler = element.getAttribute('data-confirm-handler');
                    
                    try {
                        this.safeExecute(confirmHandler);
                    } catch (error) {
                        console.error('确认操作执行失败:', error);
                    }
                });
            });
            
            // 返回确认操作
            document.querySelectorAll('[data-action="confirm-return"]').forEach(element => {
                element.addEventListener('click', (e) => {
                    const confirmHandler = element.getAttribute('data-confirm-handler');
                    
                    try {
                        const result = this.safeExecute(confirmHandler);
                        if (result === false) {
                            e.preventDefault();
                        }
                    } catch (error) {
                        console.error('确认操作执行失败:', error);
                        e.preventDefault();
                    }
                });
            });
        },
        
        // 安全执行代码
        safeExecute: function(code) {
            if (!code) return;
            
            try {
                // 使用 Function 构造器安全执行
                const func = new Function('return ' + code);
                return func();
            } catch (error) {
                // 如果作为表达式失败，尝试作为语句执行
                try {
                    const func = new Function(code);
                    return func();
                } catch (error2) {
                    console.error('代码执行失败:', error2);
                    throw error2;
                }
            }
        },
        
        // 为动态添加的元素重新绑定事件
        rebind: function(container) {
            if (container) {
                // 在指定容器内重新绑定
                const oldQuerySelectorAll = document.querySelectorAll;
                document.querySelectorAll = function(selector) {
                    return container.querySelectorAll(selector);
                };
                
                this.bindDeleteActions();
                this.bindFormValidation();
                this.bindConfirmActions();
                
                document.querySelectorAll = oldQuerySelectorAll;
            } else {
                // 重新绑定所有
                this.init();
            }
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            CriticalEventHandler.init();
        });
    } else {
        CriticalEventHandler.init();
    }
    
    // 暴露到全局，方便调试和动态绑定
    window.CriticalEventHandler = CriticalEventHandler;
    
})();'''
    
    # 保存关键事件处理器
    handler_path = 'app/static/js/critical-event-handler.js'
    
    with open(handler_path, 'w', encoding='utf-8') as f:
        f.write(handler_script)
    
    print(f"✅ 关键事件处理器已保存到: {handler_path}")

def update_base_template_critical():
    """更新基础模板以包含关键事件处理器"""
    print("\n🔧 更新基础模板...")
    
    base_template_path = 'app/templates/base.html'
    
    try:
        with open(base_template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含关键事件处理器
        if 'critical-event-handler.js' in content:
            print("✅ 基础模板已包含关键事件处理器")
            return
        
        # 在 CSP 辅助脚本之后添加关键事件处理器
        critical_handler_line = '    <script nonce="{{ csp_nonce }}" src="{{ url_for(\'static\', filename=\'js/critical-event-handler.js\') }}"></script>'
        
        # 查找 CSP 辅助脚本的位置
        csp_helper_pos = content.find('csp-helper.js')
        if csp_helper_pos != -1:
            # 找到该行的结束位置
            line_end = content.find('\n', csp_helper_pos)
            if line_end != -1:
                content = content[:line_end + 1] + critical_handler_line + '\n' + content[line_end + 1:]
        else:
            # 如果没找到，在第一个 script 标签之前插入
            first_script_pos = content.find('<script')
            if first_script_pos != -1:
                content = content[:first_script_pos] + critical_handler_line + '\n    ' + content[first_script_pos:]
        
        # 保存更新后的模板
        with open(base_template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 基础模板已更新")
        
    except Exception as e:
        print(f"❌ 更新基础模板失败: {e}")

def generate_fix_report(stats):
    """生成修复报告"""
    print(f"\n📊 关键问题修复报告")
    print("=" * 60)
    
    print(f"📈 修复统计:")
    print(f"   处理文件数: {stats['files_processed']}")
    print(f"   关键问题修复: {stats['critical_fixes']} 个")
    print(f"   重大问题修复: {stats['major_fixes']} 个")
    print(f"   备份文件数: {stats['backup_created']}")
    
    total_fixes = stats['critical_fixes'] + stats['major_fixes']
    
    if total_fixes > 0:
        print(f"\n✅ 修复成功！")
        print(f"   总修复数: {total_fixes} 个")
        print(f"   修复类型:")
        print(f"     - 删除确认功能: 已转换为安全的数据属性")
        print(f"     - 表单验证功能: 已转换为安全的验证机制")
        print(f"     - 其他确认操作: 已转换为安全的事件处理")
        
        print(f"\n🔧 修复机制:")
        print(f"   - 原有的 onclick 事件 → data-action 属性")
        print(f"   - 原有的 onsubmit 事件 → data-validation 属性")
        print(f"   - 关键事件处理器自动处理这些属性")
        print(f"   - 保持原有功能的同时符合 CSP 规范")
        
    else:
        print(f"\n✅ 没有发现需要修复的关键问题！")
    
    print(f"\n🚀 后续步骤:")
    print(f"1. 重启应用服务器")
    print(f"2. 测试删除功能是否正常工作")
    print(f"3. 测试表单验证是否正常工作")
    print(f"4. 检查浏览器控制台是否还有 CSP 错误")

if __name__ == '__main__':
    print("🚨 关键 CSP 问题修复工具")
    print("=" * 60)
    
    # 1. 修复关键问题
    stats = fix_critical_issues()
    
    # 2. 创建关键事件处理器
    create_critical_event_handler()
    
    # 3. 更新基础模板
    update_base_template_critical()
    
    print("\n" + "=" * 60)
    print("🎉 关键问题修复完成！")
    print("🔒 删除确认、表单验证等关键功能已修复")
    print("✅ 系统安全性得到保障")
    print("\n💡 提示:")
    print("现在您的项目中的关键安全功能都已经修复")
    print("删除操作会正确显示确认对话框")
    print("表单验证会正常工作")
    print("所有修改都有备份文件（.critical.backup）")
