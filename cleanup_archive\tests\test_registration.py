#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试学校注册功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.services.school_registration_service import SchoolRegistrationService
from app.models import User, AdministrativeArea, Role

def test_registration():
    """测试注册功能"""
    app = create_app()
    
    with app.app_context():
        print("开始测试学校注册功能...")
        
        # 测试数据
        test_data = {
            'school_name': '海淀区中关村第一小学测试',
            'username': 'testuser001',
            'email': '<EMAIL>',
            'real_name': '测试管理员',
            'phone': '13800138001',
            'password': 'test123456'
        }
        
        try:
            # 检查学校名称是否已存在
            if SchoolRegistrationService.check_school_name_exists(test_data['school_name']):
                print(f"❌ 学校名称 '{test_data['school_name']}' 已存在")
                return False
            
            print(f"✅ 学校名称 '{test_data['school_name']}' 可用")
            
            # 执行注册
            print("正在创建学校和用户...")
            user, school_area = SchoolRegistrationService.register_school_and_user(test_data)
            
            print(f"✅ 注册成功！")
            print(f"   学校ID: {school_area.id}")
            print(f"   学校名称: {school_area.name}")
            print(f"   学校代码: {school_area.code}")
            print(f"   用户ID: {user.id}")
            print(f"   用户名: {user.username}")
            print(f"   真实姓名: {user.real_name}")
            print(f"   区域ID: {user.area_id}")
            
            # 检查角色分配
            roles = [role.name for role in user.roles]
            print(f"   分配角色: {', '.join(roles)}")
            
            # 检查权限
            if user.has_permission('user', 'create'):
                print("✅ 用户权限检查通过")
            else:
                print("❌ 用户权限检查失败")
            
            # 检查食堂创建
            canteen = AdministrativeArea.query.filter_by(parent_id=school_area.id, level=4).first()
            if canteen:
                print(f"✅ 默认食堂创建成功: {canteen.name}")
            else:
                print("❌ 默认食堂创建失败")
            
            return True
            
        except Exception as e:
            print(f"❌ 注册失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def cleanup_test_data():
    """清理测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 删除测试学校
            test_schools = AdministrativeArea.query.filter(
                AdministrativeArea.name.like('%测试%')
            ).all()
            
            for school in test_schools:
                # 删除子区域（食堂）
                children = AdministrativeArea.query.filter_by(parent_id=school.id).all()
                for child in children:
                    db.session.delete(child)
                
                # 删除关联用户
                users = User.query.filter_by(area_id=school.id).all()
                for user in users:
                    # 删除用户角色关联
                    from app.models import UserRole
                    UserRole.query.filter_by(user_id=user.id).delete()
                    db.session.delete(user)
                
                # 删除学校
                db.session.delete(school)
            
            db.session.commit()
            print("✅ 测试数据清理完成")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 清理测试数据失败: {e}")

if __name__ == '__main__':
    print("=" * 60)
    print("学校注册功能测试")
    print("=" * 60)
    
    # 清理之前的测试数据
    cleanup_test_data()
    
    # 执行测试
    success = test_registration()
    
    if success:
        print("\n🎉 测试通过！学校注册功能正常工作")
    else:
        print("\n❌ 测试失败！请检查错误信息")
    
    print("\n" + "=" * 60)
