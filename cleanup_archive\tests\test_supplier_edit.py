#!/usr/bin/env python3
"""
测试供应商编辑功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, SupplierSchoolRelation, AdministrativeArea

def test_supplier_edit():
    """测试供应商编辑功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 供应商编辑功能测试 ===\n")
        
        # 1. 查找一个有学校关系的供应商
        print("1. 查找测试供应商...")
        try:
            # 查找有学校关系的供应商
            supplier = Supplier.query.join(SupplierSchoolRelation).filter(
                SupplierSchoolRelation.status == 1
            ).first()
            
            if not supplier:
                print("   ❌ 没有找到有学校关系的供应商")
                return False
            
            print(f"   ✅ 找到测试供应商: {supplier.name} (ID: {supplier.id})")
            
            # 检查学校关系
            relations = supplier.school_relations
            print(f"   ✅ 该供应商有 {len(relations)} 个学校关系")
            
            for relation in relations:
                if relation.status == 1:
                    print(f"   - 学校: {relation.area.name}")
                    print(f"   - 合同编号: {relation.contract_number}")
                    print(f"   - 状态: {'有效' if relation.status == 1 else '已终止'}")
            
        except Exception as e:
            print(f"   ❌ 查找供应商失败: {e}")
            return False
        
        # 2. 测试表单创建
        print("\n2. 测试编辑表单创建...")
        try:
            from app.forms.supplier import SupplierForm
            
            # 创建编辑模式的表单
            form = SupplierForm(obj=supplier, edit_mode=True)
            
            # 设置choices
            from app.models import SupplierCategory
            categories = SupplierCategory.query.all()
            form.category_id.choices = [(0, '-- 请选择分类 --')] + [(c.id, c.name) for c in categories]
            form.area_id.choices = [(0, '-- 请选择学校 --')]
            form.relation_status.choices = [(1, '有效'), (0, '已终止')]
            
            print("   ✅ 表单创建成功")
            print(f"   - 供应商名称: {form.name.data}")
            print(f"   - 联系人: {form.contact_person.data}")
            print(f"   - 电话: {form.phone.data}")
            print(f"   - 编辑模式: {form.edit_mode}")
            
        except Exception as e:
            print(f"   ❌ 表单创建失败: {e}")
            return False
        
        # 3. 测试字段验证
        print("\n3. 测试字段验证...")
        try:
            # 在编辑模式下，学校关系字段应该是可选的
            area_id_validators = [v.__class__.__name__ for v in form.area_id.validators]
            start_date_validators = [v.__class__.__name__ for v in form.start_date.validators]
            
            print(f"   ✅ area_id 验证器: {area_id_validators}")
            print(f"   ✅ start_date 验证器: {start_date_validators}")
            
            # 检查是否包含Optional验证器
            if 'Optional' in area_id_validators:
                print("   ✅ 学校字段在编辑模式下是可选的")
            else:
                print("   ❌ 学校字段在编辑模式下不是可选的")
                return False
                
        except Exception as e:
            print(f"   ❌ 字段验证测试失败: {e}")
            return False
        
        # 4. 测试模板数据
        print("\n4. 测试模板数据...")
        try:
            # 检查供应商是否有学校关系数据
            if hasattr(supplier, 'school_relations'):
                print(f"   ✅ 供应商有 school_relations 属性")
                print(f"   ✅ 学校关系数量: {len(supplier.school_relations)}")
                
                for relation in supplier.school_relations:
                    print(f"   - 关系ID: {relation.id}")
                    print(f"   - 学校: {relation.area.name}")
                    print(f"   - 合同: {relation.contract_number}")
            else:
                print("   ❌ 供应商没有 school_relations 属性")
                return False
                
        except Exception as e:
            print(f"   ❌ 模板数据测试失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！供应商编辑功能正常工作。")
        print(f"✅ 测试的供应商ID: {supplier.id}")
        return True

if __name__ == '__main__':
    success = test_supplier_edit()
    sys.exit(0 if success else 1)
