#!/usr/bin/env python3
"""
文件上传和CSP修复测试脚本
"""

import os
import sys
import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def test_csp_headers():
    """测试CSP头是否正确设置"""
    print("🔍 测试CSP头设置...")
    
    try:
        # 测试主页
        response = requests.get('http://localhost:5000/', timeout=10)
        csp_header = response.headers.get('Content-Security-Policy')
        
        if csp_header:
            print(f"✅ CSP头已设置: {csp_header}")
            
            # 检查关键策略
            required_policies = [
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
                "style-src 'self' 'unsafe-inline'",
                "img-src 'self' data: blob:"
            ]
            
            for policy in required_policies:
                if policy in csp_header:
                    print(f"✅ 包含必要策略: {policy}")
                else:
                    print(f"❌ 缺少策略: {policy}")
        else:
            print("❌ 未找到CSP头")
            
    except requests.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    return True

def test_file_upload_ui():
    """测试文件上传UI功能"""
    print("\n🔍 测试文件上传UI功能...")
    
    # Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # 访问文件上传页面（需要根据实际URL调整）
        test_urls = [
            'http://localhost:5000/trace-document/upload/1',  # 示例URL
            'http://localhost:5000/',  # 主页
        ]
        
        for url in test_urls:
            try:
                print(f"📄 测试页面: {url}")
                driver.get(url)
                
                # 等待页面加载
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 检查是否有CSP错误
                logs = driver.get_log('browser')
                csp_errors = [log for log in logs if 'Content Security Policy' in log.get('message', '')]
                
                if csp_errors:
                    print("❌ 发现CSP错误:")
                    for error in csp_errors:
                        print(f"   {error['message']}")
                else:
                    print("✅ 无CSP错误")
                
                # 查找文件输入元素
                file_inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                
                if file_inputs:
                    print(f"✅ 找到 {len(file_inputs)} 个文件输入元素")
                    
                    for i, file_input in enumerate(file_inputs):
                        # 检查元素是否可见和可点击
                        is_displayed = file_input.is_displayed()
                        is_enabled = file_input.is_enabled()
                        
                        print(f"   文件输入 {i+1}: 显示={is_displayed}, 启用={is_enabled}")
                        
                        # 检查是否有修复脚本的标记
                        upload_fixed = file_input.get_attribute('data-upload-fixed')
                        if upload_fixed:
                            print(f"   ✅ 文件输入 {i+1} 已被修复脚本处理")
                        else:
                            print(f"   ⚠️ 文件输入 {i+1} 可能未被修复脚本处理")
                
                else:
                    print("ℹ️ 页面中未找到文件输入元素")
                
                # 检查修复脚本是否加载
                script_loaded = driver.execute_script("""
                    return typeof window.FileUploadFix !== 'undefined';
                """)
                
                if script_loaded:
                    print("✅ 文件上传修复脚本已加载")
                else:
                    print("❌ 文件上传修复脚本未加载")
                
            except TimeoutException:
                print(f"❌ 页面加载超时: {url}")
            except Exception as e:
                print(f"❌ 测试页面时出错: {e}")
        
        return True
        
    except WebDriverException as e:
        print(f"❌ WebDriver错误: {e}")
        print("💡 提示: 请确保已安装Chrome浏览器和ChromeDriver")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def test_static_files():
    """测试静态文件是否存在"""
    print("\n🔍 测试静态文件...")
    
    static_files = [
        'app/static/js/file-upload-fix.js',
        'app/templates/base.html',
        'docs/file-upload-csp-fix.md'
    ]
    
    all_exist = True
    for file_path in static_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("🚀 开始文件上传和CSP修复测试\n")
    
    # 检查Flask应用是否运行
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        print("✅ Flask应用正在运行")
    except requests.RequestException:
        print("❌ Flask应用未运行，请先启动应用")
        print("💡 运行命令: python run.py")
        return False
    
    # 运行测试
    tests = [
        ("静态文件检查", test_static_files),
        ("CSP头测试", test_csp_headers),
        ("文件上传UI测试", test_file_upload_ui),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print("📊 测试结果摘要")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
