{% extends 'base.html' %}

{% block title %}注册 - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8 col-md-10 col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-school mr-2"></i>
                    创建学校并注册
                </h4>
                <p class="mb-0 mt-2 text-center">
                    <small>注册后您将成为学校管理员，拥有完整的系统管理权限</small>
                </p>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}

                    <!-- 学校信息 -->
                    <div class="form-group">
                        <h5 class="text-primary">学校信息</h5>
                        <hr>
                    </div>
                    <div class="form-group">
                        {{ form.school_name.label }}
                        {{ form.school_name(class="form-control", placeholder="如：海淀区中关村第一小学", id="school_name_input") }}
                        {% for error in form.school_name.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            请输入完整的学校名称，包含地区信息，不少于6个汉字<br>
                            <strong>格式示例：</strong>海淀区中关村第一小学、朝阳区实验中学、丰台区第二幼儿园
                        </small>
                    </div>

                    <!-- 管理员信息 -->
                    <div class="form-group mt-4">
                        <h5 class="text-primary">管理员信息</h5>
                        <hr>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-12 mobile-mb-2">
                            <div class="form-group">
                                {{ form.username.label(class="font-weight-bold") }}
                                <div class="input-group">
                                    {{ form.username(class="form-control", placeholder="用于登录的用户名", id="username_input") }}
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-secondary" id="generate_username_btn" title="根据学校名称生成用户名">
                                            <i class="fas fa-magic"></i>
                                        </button>
                                    </div>
                                </div>
                                {% for error in form.username.errors %}
                                <small class="text-danger d-block mt-1">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </small>
                                {% endfor %}
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb"></i>
                                    可点击魔法按钮根据学校名称自动生成用户名
                                </small>
                            </div>
                        </div>
                        <div class="col-lg-6 col-12 mobile-mb-2">
                            <div class="form-group">
                                {{ form.email.label(class="font-weight-bold") }}
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                    </div>
                                    {{ form.email(class="form-control", placeholder="您的邮箱地址") }}
                                </div>
                                {% for error in form.email.errors %}
                                <small class="text-danger d-block mt-1">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-12 mobile-mb-2">
                            <div class="form-group">
                                {{ form.real_name.label(class="font-weight-bold") }}
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                    {{ form.real_name(class="form-control", placeholder="您的真实姓名") }}
                                </div>
                                {% for error in form.real_name.errors %}
                                <small class="text-danger d-block mt-1">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-lg-6 col-12 mobile-mb-2">
                            <div class="form-group">
                                {{ form.phone.label(class="font-weight-bold") }}
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-phone"></i>
                                        </span>
                                    </div>
                                    {{ form.phone(class="form-control", placeholder="您的手机号码") }}
                                </div>
                                {% for error in form.phone.errors %}
                                <small class="text-danger d-block mt-1">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-12 mobile-mb-2">
                            <div class="form-group">
                                {{ form.password.label(class="font-weight-bold") }}
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    </div>
                                    {{ form.password(class="form-control", placeholder="设置登录密码") }}
                                </div>
                                {% for error in form.password.errors %}
                                <small class="text-danger d-block mt-1">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-lg-6 col-12 mobile-mb-2">
                            <div class="form-group">
                                {{ form.password2.label(class="font-weight-bold") }}
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    </div>
                                    {{ form.password2(class="form-control", placeholder="再次输入密码") }}
                                </div>
                                {% for error in form.password2.errors %}
                                <small class="text-danger d-block mt-1">
                                    <i class="fas fa-exclamation-circle"></i> {{ error }}
                                </small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- 注册说明 -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 注册说明</h6>
                        <ul class="mb-0">
                            <li><strong>学校名称：</strong>请输入完整的学校全称，包含地区信息，如"海淀区中关村第一小学"</li>
                            <li><strong>用户名：</strong>可使用魔法按钮根据学校名称自动生成，也可自定义</li>
                            <li><strong>管理权限：</strong>注册成功后您将获得学校管理员权限，可管理所有功能</li>
                            <li><strong>数据独立：</strong>您的学校数据完全独立，不会与其他学校混淆</li>
                            <li><strong>完全免费：</strong>系统完全免费使用，无需审核，注册即可开始使用</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary btn-block btn-lg") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">已有账号？<a href="{{ url_for('auth.login') }}">立即登录</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 拼音首字母映射表
    const pinyinMap = {
        '北': 'B', '京': 'J', '上': 'S', '海': 'H', '天': 'T', '津': 'J',
        '重': 'C', '庆': 'Q', '河': 'H', '山': 'S', '西': 'X',
        '内': 'N', '蒙': 'M', '古': 'G', '辽': 'L', '宁': 'N', '吉': 'J',
        '林': 'L', '黑': 'H', '龙': 'L', '江': 'J', '苏': 'S', '浙': 'Z',
        '安': 'A', '徽': 'H', '福': 'F', '建': 'J', '东': 'D',
        '南': 'N', '湖': 'H', '广': 'G', '四': 'S', '川': 'C', '贵': 'G',
        '州': 'Z', '云': 'Y', '陕': 'S', '甘': 'G', '肃': 'S', '青': 'Q',
        '夏': 'X', '新': 'X', '疆': 'J', '台': 'T', '湾': 'W',
        '香': 'X', '港': 'G', '澳': 'A', '门': 'M', '市': 'S', '区': 'Q',
        '县': 'X', '镇': 'Z', '乡': 'X', '村': 'C', '学': 'X', '校': 'X',
        '小': 'X', '中': 'Z', '大': 'D', '第': 'D', '一': 'Y', '二': 'E',
        '三': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B',
        '九': 'J', '十': 'S', '实': 'S', '验': 'Y', '民': 'M', '族': 'Z',
        '外': 'W', '国': 'G', '语': 'Y', '职': 'Z', '业': 'Y', '技': 'J',
        '术': 'S', '师': 'S', '范': 'F', '幼': 'Y', '儿': 'E', '园': 'Y'
    };

    // 生成用户名建议
    function generateUsername(schoolName) {
        if (!schoolName || schoolName.length < 3) {
            return '';
        }

        // 提取拼音首字母
        let initials = '';
        for (let char of schoolName) {
            if (pinyinMap[char]) {
                initials += pinyinMap[char];
            } else if (/[a-zA-Z]/.test(char)) {
                initials += char.toUpperCase();
            }
        }

        // 限制长度
        if (initials.length > 8) {
            initials = initials.substring(0, 8);
        } else if (initials.length < 4) {
            initials = (initials + 'XXXX').substring(0, 4);
        }

        // 添加随机数字
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return (initials + randomNum).toLowerCase();
    }

    // 生成用户名按钮点击事件
    $('#generate_username_btn').click(function() {
        const schoolName = $('#school_name_input').val().trim();
        if (!schoolName) {
            alert('请先输入学校名称');
            $('#school_name_input').focus();
            return;
        }

        if (schoolName.length < 6) {
            alert('学校名称不能少于6个汉字');
            $('#school_name_input').focus();
            return;
        }

        const suggestedUsername = generateUsername(schoolName);
        $('#username_input').val(suggestedUsername);

        // 显示生成提示
        const $btn = $(this);
        const originalHtml = $btn.html();
        $btn.html('<i class="fas fa-check text-success"></i>');
        setTimeout(function() {
            $btn.html(originalHtml);
        }, 1500);
    });

    // 学校名称输入时的实时提示
    $('#school_name_input').on('input', function() {
        const value = $(this).val().trim();
        const chineseChars = value.match(/[\u4e00-\u9fff]/g);
        const chineseCount = chineseChars ? chineseChars.length : 0;

        // 更新字符计数提示
        let $hint = $(this).siblings('.char-count-hint');
        if ($hint.length === 0) {
            $hint = $('<small class="char-count-hint text-info"></small>');
            $(this).parent().append($hint);
        }

        if (chineseCount < 6) {
            $hint.html(`<i class="fas fa-info-circle"></i> 已输入 ${chineseCount} 个汉字，还需 ${6 - chineseCount} 个`);
            $hint.removeClass('text-success').addClass('text-info');
        } else {
            $hint.html(`<i class="fas fa-check-circle"></i> 格式正确，共 ${chineseCount} 个汉字`);
            $hint.removeClass('text-info').addClass('text-success');
        }
    });
});
</script>
{% endblock %}
