#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蔬菜图片智能筛选和压缩工具
从湘菜图片文件夹中筛选出纯蔬菜类图片，并压缩到指定尺寸
"""

import os
import re
import shutil
from pathlib import Path
from PIL import Image, ImageOps
from datetime import datetime
from typing import List, Set, Dict
import json

class VegetableImageExtractor:
    def __init__(self, source_folder: str = "D:/BaiduNetdiskDownload/xiangchai", 
                 target_folder: str = "D:/蔬菜图片", target_size: tuple = (400, 400)):
        """
        初始化蔬菜图片提取器
        
        Args:
            source_folder: 源图片文件夹路径
            target_folder: 目标文件夹路径
            target_size: 目标图片尺寸
        """
        self.source_folder = Path(source_folder)
        self.target_folder = Path(target_folder)
        self.target_size = target_size
        self.quality = 85
        
        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.JPG', '.JPEG', '.png', '.PNG'}
        
        # 统计信息
        self.stats = {
            'total_images': 0,
            'vegetable_images': 0,
            'processed_images': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
        
        self.errors = []
        self.processed_files = []
        
        # 蔬菜关键词列表（纯蔬菜，不包含肉类）
        self.vegetable_keywords = {
            # 叶菜类
            '白菜', '大白菜', '小白菜', '上海青', '青菜', '菠菜', '韭菜', '韭黄', '芹菜', '生菜',
            '苋菜', '红苋菜', '菜心', '芥菜', '油菜', '茼蒿', '空心菜', '通菜', '菜花', '花菜',
            '西兰花', '花椰菜', '包菜', '卷心菜', '圆白菜', '紫甘蓝',
            
            # 根茎类
            '萝卜', '胡萝卜', '白萝卜', '青萝卜', '土豆', '马铃薯', '红薯', '地瓜', '山药', '淮山',
            '芋头', '芋艿', '莲藕', '藕', '竹笋', '笋', '冬笋', '春笋', '毛笋', '莴笋', '莴苣',
            '洋葱', '大葱', '小葱', '韭葱', '蒜', '大蒜', '蒜苗', '蒜苔', '蒜台', '生姜', '姜',
            
            # 瓜果类
            '冬瓜', '南瓜', '丝瓜', '苦瓜', '黄瓜', '青瓜', '西葫芦', '葫芦', '佛手瓜',
            '茄子', '西红柿', '番茄', '青椒', '辣椒', '尖椒', '甜椒', '彩椒', '红椒', '绿椒',
            '柿子椒', '朝天椒', '小米椒',
            
            # 豆类蔬菜
            '豆角', '四季豆', '刀豆', '扁豆', '豇豆', '长豆角', '豆苗', '豌豆苗', '豆芽', '绿豆芽',
            '黄豆芽', '豆芽菜', '蚕豆', '豌豆', '毛豆', '荷兰豆', '豆荚',
            
            # 菌菇类
            '蘑菇', '香菇', '平菇', '杏鲍菇', '金针菇', '茶树菇', '草菇', '口蘑', '白灵菇',
            '猴头菇', '木耳', '黑木耳', '银耳', '白木耳', '地木耳', '地软', '地皮菜',
            
            # 其他蔬菜
            '芽菜', '豆苗', '花生芽', '萝卜苗', '紫菜', '海带', '裙带菜', '海白菜',
            '莼菜', '马齿苋', '苦菊', '蒲公英', '荠菜', '香椿', '槐花',
        }
        
        # 排除关键词（包含这些词的不是纯蔬菜）
        self.exclude_keywords = {
            # 肉类
            '肉', '猪', '牛', '羊', '鸡', '鸭', '鹅', '鱼', '虾', '蟹', '贝', '螺', '蚌', '蛤',
            '排骨', '肋骨', '脊骨', '筒骨', '棒骨', '肘子', '蹄', '爪', '翅', '腿', '胸',
            '肝', '肾', '心', '肺', '肚', '肠', '舌', '脑', '血', '骨', '筋', '皮',
            '腊肉', '香肠', '火腿', '培根', '咸肉', '腌肉', '熏肉', '烤肉', '卤肉',
            '牛柳', '牛腩', '牛仔骨', '牛黄喉', '牛百叶', '牛肚', '牛筋',
            '猪柳', '猪颈肉', '猪手', '猪脚', '猪尾', '猪耳', '猪血', '猪肝', '猪腰',
            '鸡柳', '鸡胸', '鸡腿', '鸡翅', '鸡爪', '鸡胗', '鸡肝', '鸡心', '鸡杂',
            '鸭掌', '鸭舌', '鸭血', '鸭肝', '鸭胗', '鸭肠', '鸭脖',
            '鱼片', '鱼头', '鱼尾', '鱼肚', '鱼唇', '鱼翅', '鱼胶',
            '虾仁', '虾球', '虾饺', '虾滑', '基围虾', '明虾', '对虾',
            '蟹肉', '蟹黄', '蟹钳', '螃蟹', '大闸蟹', '梭子蟹',
            
            # 蛋类
            '蛋', '鸡蛋', '鸭蛋', '鹅蛋', '鹌鹑蛋', '皮蛋', '咸蛋', '茶蛋',
            
            # 奶制品
            '奶', '牛奶', '酸奶', '奶酪', '芝士', '黄油', '奶油',
            
            # 海鲜
            '海参', '鲍鱼', '扇贝', '生蚝', '牡蛎', '海胆', '海蜇', '鱿鱼', '墨鱼', '章鱼',
            '带鱼', '黄鱼', '鲈鱼', '鲫鱼', '草鱼', '鲤鱼', '鲢鱼', '桂鱼', '石斑鱼',
            
            # 其他动物性食品
            '燕窝', '鱼胶', '花胶', '鱼翅', '海参', '鲍鱼', '干贝', '瑶柱',
        }
        
        # 文件名清理规则
        self.name_cleaning_patterns = [
            (r'^\d+\.?\s*', ''),  # 去除开头数字
            (r'^\d+[-_]\s*', ''),  # 去除开头数字和分隔符
            (r'\([^)]*\)', ''),   # 去除括号内容
            (r'\[[^\]]*\]', ''),  # 去除方括号内容
            (r'（[^）]*）', ''),   # 去除中文括号内容
            (r'[_\-\s]*副本\d*$', ''),  # 去除副本后缀
            (r'[_\-\s]*copy\d*$', ''),  # 去除copy后缀
            (r'[_\-\s]*备份\d*$', ''),  # 去除备份后缀
            (r'[_\-\s]*tuya$', ''),     # 去除tuya后缀
            (r'[_\-\s]*调$', ''),       # 去除调后缀
            (r'[_\-\s]*\d+$', ''),      # 去除结尾数字
            (r'[_\-]+', ''),            # 去除分隔符
            (r'\s+', ''),               # 去除空格
        ]

    def normalize_filename(self, filename: str) -> str:
        """
        标准化文件名
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 标准化后的文件名
        """
        if not filename:
            return ''
        
        # 去除扩展名
        name = Path(filename).stem
        
        # 应用清理规则
        for pattern, replacement in self.name_cleaning_patterns:
            name = re.sub(pattern, replacement, name, flags=re.IGNORECASE)
        
        return name.strip()

    def is_vegetable_image(self, filename: str) -> bool:
        """
        判断是否为纯蔬菜图片
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否为蔬菜图片
        """
        normalized_name = self.normalize_filename(filename).lower()
        
        # 首先检查是否包含排除关键词
        for exclude_word in self.exclude_keywords:
            if exclude_word in normalized_name:
                return False
        
        # 检查是否包含蔬菜关键词
        for veg_word in self.vegetable_keywords:
            if veg_word in normalized_name:
                return True
        
        return False

    def compress_and_save_image(self, source_path: Path, target_path: Path) -> bool:
        """
        压缩并保存图片
        
        Args:
            source_path: 源图片路径
            target_path: 目标图片路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            with Image.open(source_path) as img:
                # 转换为RGB模式（处理RGBA、P等模式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 使用高质量重采样算法调整大小
                img = ImageOps.fit(img, self.target_size, Image.Resampling.LANCZOS)
                
                # 保存压缩后的图片
                img.save(target_path, 'JPEG', quality=self.quality, optimize=True)
                
                return True
                
        except Exception as e:
            self.errors.append(f"图片压缩失败 {source_path}: {e}")
            return False

    def scan_and_extract_vegetables(self) -> Dict:
        """
        扫描并提取蔬菜图片
        
        Returns:
            Dict: 处理报告
        """
        self.stats['start_time'] = datetime.now()
        
        print(f"📂 扫描源文件夹: {self.source_folder}")
        print(f"🎯 目标文件夹: {self.target_folder}")
        print(f"📏 目标尺寸: {self.target_size[0]}x{self.target_size[1]}")
        
        if not self.source_folder.exists():
            raise FileNotFoundError(f"源文件夹不存在: {self.source_folder}")
        
        # 获取所有图片文件
        image_files = []
        for ext in self.image_extensions:
            image_files.extend(self.source_folder.glob(f"*{ext}"))
        
        self.stats['total_images'] = len(image_files)
        print(f"🖼️ 找到图片文件: {len(image_files)} 个")
        
        # 筛选蔬菜图片
        vegetable_files = []
        for image_file in image_files:
            if self.is_vegetable_image(image_file.name):
                vegetable_files.append(image_file)
        
        self.stats['vegetable_images'] = len(vegetable_files)
        print(f"🥬 识别蔬菜图片: {len(vegetable_files)} 个")
        
        if not vegetable_files:
            print("❌ 没有找到蔬菜图片")
            return self.generate_report()
        
        # 显示一些蔬菜图片示例
        print(f"\n📋 蔬菜图片示例 (前10个):")
        for i, veg_file in enumerate(vegetable_files[:10], 1):
            normalized_name = self.normalize_filename(veg_file.name)
            file_size = veg_file.stat().st_size
            print(f"  {i}. {veg_file.name} → {normalized_name} ({file_size:,} 字节)")
        
        if len(vegetable_files) > 10:
            print(f"  ... 还有 {len(vegetable_files) - 10} 个蔬菜图片")
        
        # 询问是否继续
        response = input(f"\n是否开始提取和压缩这 {len(vegetable_files)} 个蔬菜图片？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return self.generate_report()
        
        # 处理蔬菜图片
        print(f"\n🚀 开始处理蔬菜图片...")
        
        for i, veg_file in enumerate(vegetable_files, 1):
            progress = (i / len(vegetable_files)) * 100
            print(f"[{progress:.1f}%] 处理: {veg_file.name}")
            
            # 生成目标文件名
            target_filename = f"vegetable_{i:04d}_{self.normalize_filename(veg_file.name)}.jpg"
            target_path = self.target_folder / target_filename
            
            # 压缩并保存图片
            if self.compress_and_save_image(veg_file, target_path):
                self.stats['processed_images'] += 1
                self.processed_files.append({
                    'source': str(veg_file),
                    'target': str(target_path),
                    'original_size': veg_file.stat().st_size,
                    'compressed_size': target_path.stat().st_size if target_path.exists() else 0
                })
                print(f"  ✅ 成功: {target_filename}")
            else:
                self.stats['errors'] += 1
                print(f"  ❌ 失败: {veg_file.name}")
        
        self.stats['end_time'] = datetime.now()
        
        print(f"\n✅ 处理完成！")
        print(f"📊 成功处理: {self.stats['processed_images']} 个图片")
        print(f"❌ 处理失败: {self.stats['errors']} 个图片")
        
        return self.generate_report()

    def generate_report(self) -> Dict:
        """生成处理报告"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
        
        # 计算压缩比
        total_original_size = sum(f['original_size'] for f in self.processed_files)
        total_compressed_size = sum(f['compressed_size'] for f in self.processed_files)
        compression_ratio = (1 - total_compressed_size / max(total_original_size, 1)) * 100
        
        report = {
            'summary': {
                'total_images_scanned': self.stats['total_images'],
                'vegetable_images_found': self.stats['vegetable_images'],
                'images_processed': self.stats['processed_images'],
                'errors_occurred': self.stats['errors'],
                'duration_seconds': duration.total_seconds() if duration else None,
                'success_rate': (self.stats['processed_images'] / max(self.stats['vegetable_images'], 1)) * 100,
                'total_original_size_mb': total_original_size / (1024 * 1024),
                'total_compressed_size_mb': total_compressed_size / (1024 * 1024),
                'compression_ratio_percent': compression_ratio,
                'target_folder': str(self.target_folder),
                'target_size': f"{self.target_size[0]}x{self.target_size[1]}"
            },
            'processed_files': self.processed_files,
            'errors': self.errors,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存报告到文件
        report_filename = f"vegetable_extraction_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            report['report_file'] = report_filename
        except Exception as e:
            print(f"⚠️ 保存报告文件失败: {e}")
        
        return report

def main():
    """主函数"""
    print("🥬 **蔬菜图片智能筛选和压缩工具**")
    print("="*60)
    print("从湘菜图片文件夹中筛选出纯蔬菜类图片，并压缩到400x400尺寸")
    print("="*60)
    
    try:
        # 初始化提取器
        extractor = VegetableImageExtractor()
        
        # 执行扫描和提取
        report = extractor.scan_and_extract_vegetables()
        
        # 显示最终统计
        if report['summary']['images_processed'] > 0:
            print(f"\n🎉 蔬菜图片提取成功！")
            print(f"📊 提取成果:")
            print(f"  🥬 蔬菜图片: {report['summary']['vegetable_images_found']} 个")
            print(f"  ✅ 成功处理: {report['summary']['images_processed']} 个")
            print(f"  📏 目标尺寸: {report['summary']['target_size']}")
            print(f"  💾 压缩比: {report['summary']['compression_ratio_percent']:.1f}%")
            print(f"  📂 保存位置: {report['summary']['target_folder']}")
            
            if 'report_file' in report:
                print(f"  📄 详细报告: {report['report_file']}")
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
