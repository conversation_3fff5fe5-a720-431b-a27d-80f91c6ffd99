"""添加菜单与消耗计划模块的溯源功能相关表和字段

Revision ID: ec0b1e5c3e98
Revises: 3809f5e34d99
Create Date: 2025-05-05 18:09:44.856279

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ec0b1e5c3e98'
down_revision = '3809f5e34d99'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('menu_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('area_id', sa.Integer(), nullable=False),
    sa.Column('plan_date', sa.Date(), nullable=False),
    sa.Column('meal_type', sa.String(length=20), nullable=False),
    sa.Column('expected_diners', sa.Integer(), nullable=True),
    sa.Column('actual_diners', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('approved_by', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['area_id'], ['administrative_areas.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('consumption_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('menu_plan_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=False),
    sa.Column('approved_by', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['menu_plan_id'], ['menu_plans.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('menu_recipes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('menu_plan_id', sa.Integer(), nullable=False),
    sa.Column('recipe_id', sa.Integer(), nullable=False),
    sa.Column('planned_quantity', sa.Float(), nullable=False),
    sa.Column('actual_quantity', sa.Float(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['menu_plan_id'], ['menu_plans.id'], ),
    sa.ForeignKeyConstraint(['recipe_id'], ['recipes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('consumption_details',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('consumption_plan_id', sa.Integer(), nullable=False),
    sa.Column('ingredient_id', sa.Integer(), nullable=False),
    sa.Column('planned_quantity', sa.Float(), nullable=False),
    sa.Column('actual_quantity', sa.Float(), nullable=True),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['consumption_plan_id'], ['consumption_plans.id'], ),
    sa.ForeignKeyConstraint(['ingredient_id'], ['ingredients.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('food_samples', schema=None) as batch_op:
        batch_op.add_column(sa.Column('sample_number', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('area_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('menu_plan_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('meal_date', sa.Date(), nullable=True))
        batch_op.add_column(sa.Column('meal_type', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('sample_quantity', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('sample_unit', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('storage_temperature', sa.String(length=50), nullable=True))
        batch_op.add_column(sa.Column('status', sa.String(length=20), server_default='已留样', nullable=False))
        batch_op.add_column(sa.Column('destruction_time', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('destruction_operator_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
        batch_op.create_foreign_key('fk_food_sample_destruction_operator', 'users', ['destruction_operator_id'], ['id'])
        batch_op.create_foreign_key('fk_food_sample_menu_plan', 'menu_plans', ['menu_plan_id'], ['id'])
        batch_op.create_foreign_key('fk_food_sample_area', 'administrative_areas', ['area_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('food_samples', schema=None) as batch_op:
        batch_op.drop_constraint('fk_food_sample_area', type_='foreignkey')
        batch_op.drop_constraint('fk_food_sample_menu_plan', type_='foreignkey')
        batch_op.drop_constraint('fk_food_sample_destruction_operator', type_='foreignkey')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('destruction_operator_id')
        batch_op.drop_column('destruction_time')
        batch_op.drop_column('status')
        batch_op.drop_column('storage_temperature')
        batch_op.drop_column('sample_unit')
        batch_op.drop_column('sample_quantity')
        batch_op.drop_column('meal_type')
        batch_op.drop_column('meal_date')
        batch_op.drop_column('menu_plan_id')
        batch_op.drop_column('area_id')
        batch_op.drop_column('sample_number')

    op.drop_table('consumption_details')
    op.drop_table('menu_recipes')
    op.drop_table('consumption_plans')
    op.drop_table('menu_plans')
    # ### end Alembic commands ###
