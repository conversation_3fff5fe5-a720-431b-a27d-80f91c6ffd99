#!/usr/bin/env python
"""
CSRF表单修复工具

此脚本自动修复HTML模板中缺少CSRF令牌的表单。
它会在所有POST表单中添加CSRF令牌字段。
"""

import os
import re
import sys

# 定义要扫描的目录
TEMPLATES_DIR = 'app/templates'

# 定义正则表达式模式
FORM_PATTERN = re.compile(r'<form[^>]*method=["\']post["\'][^>]*>(.*?)</form>', re.IGNORECASE | re.DOTALL)
CSRF_TOKEN_PATTERN = re.compile(r'(csrf_token\(\)|form\.hidden_tag\(\)|form\.csrf_token|name=["\']csrf_token["\'])', re.IGNORECASE)
CSRF_TOKEN_HTML = '<input type="hidden" name="csrf_token" value="{{ csrf_token() }}">'

def fix_html_file(file_path):
    """修复HTML文件中缺少CSRF令牌的表单"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有表单
    forms = FORM_PATTERN.findall(content)
    fixed_count = 0
    
    # 检查每个表单是否包含CSRF令牌
    for form_content in forms:
        if not CSRF_TOKEN_PATTERN.search(form_content):
            # 在表单开始标签后添加CSRF令牌
            new_form_content = form_content.lstrip() + '\n    ' + CSRF_TOKEN_HTML
            content = content.replace(form_content, new_form_content)
            fixed_count += 1
    
    # 如果有修复，写回文件
    if fixed_count > 0:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已修复 {file_path} 中的 {fixed_count} 个表单")
    
    return fixed_count

def main():
    """主函数"""
    print("===== CSRF表单修复工具 =====")
    
    total_files = 0
    total_forms_fixed = 0
    
    for root, dirs, files in os.walk(TEMPLATES_DIR):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)
                
                forms_fixed = fix_html_file(file_path)
                if forms_fixed > 0:
                    total_files += 1
                    total_forms_fixed += forms_fixed
    
    print(f"\n===== 修复结果摘要 =====")
    print(f"已修复 {total_files} 个文件中的 {total_forms_fixed} 个表单")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
