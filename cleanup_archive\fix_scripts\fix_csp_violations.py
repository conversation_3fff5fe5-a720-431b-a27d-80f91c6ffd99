#!/usr/bin/env python3
"""
CSP 违规批量修复脚本
自动为所有模板文件中的内联脚本和样式添加 nonce
"""

import os
import re
import glob
from pathlib import Path

def fix_csp_violations():
    """批量修复 CSP 违规问题"""
    print("🔧 开始批量修复 CSP 违规问题...")
    
    # 查找所有 HTML 模板文件
    template_patterns = [
        'app/templates/**/*.html',
        'app/templates/*.html'
    ]
    
    html_files = []
    for pattern in template_patterns:
        html_files.extend(glob.glob(pattern, recursive=True))
    
    print(f"📁 找到 {len(html_files)} 个 HTML 文件")
    
    fixed_files = 0
    total_fixes = 0
    
    for file_path in html_files:
        print(f"\n🔍 检查文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_in_file = 0
            
            # 修复内联 <script> 标签
            script_pattern = r'<script(?![^>]*nonce=)(?![^>]*src=)([^>]*)>'
            def fix_script(match):
                attrs = match.group(1)
                if 'nonce=' not in attrs:
                    return f'<script nonce="{{{{ csp_nonce }}}}"{ attrs}>'
                return match.group(0)
            
            new_content = re.sub(script_pattern, fix_script, content, flags=re.IGNORECASE)
            if new_content != content:
                script_fixes = len(re.findall(script_pattern, content, flags=re.IGNORECASE))
                fixes_in_file += script_fixes
                print(f"  ✅ 修复了 {script_fixes} 个内联 script 标签")
                content = new_content
            
            # 修复内联 <style> 标签
            style_pattern = r'<style(?![^>]*nonce=)([^>]*)>'
            def fix_style(match):
                attrs = match.group(1)
                if 'nonce=' not in attrs:
                    return f'<style nonce="{{{{ csp_nonce }}}}"{ attrs}>'
                return match.group(0)
            
            new_content = re.sub(style_pattern, fix_style, content, flags=re.IGNORECASE)
            if new_content != content:
                style_fixes = len(re.findall(style_pattern, content, flags=re.IGNORECASE))
                fixes_in_file += style_fixes
                print(f"  ✅ 修复了 {style_fixes} 个内联 style 标签")
                content = new_content
            
            # 修复带有 onclick 等事件的元素（可选）
            # 这些通常需要手动处理，但我们可以标记出来
            event_attrs = ['onclick', 'onload', 'onchange', 'onsubmit', 'onerror']
            for attr in event_attrs:
                pattern = rf'{attr}="[^"]*"'
                matches = re.findall(pattern, content, flags=re.IGNORECASE)
                if matches:
                    print(f"  ⚠️ 发现 {len(matches)} 个 {attr} 事件属性，建议手动迁移到外部脚本")
            
            # 如果有修改，保存文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixed_files += 1
                total_fixes += fixes_in_file
                print(f"  💾 已保存修复，共 {fixes_in_file} 处修改")
            else:
                print(f"  ✅ 文件无需修复")
                
        except Exception as e:
            print(f"  ❌ 处理文件失败: {e}")
    
    print(f"\n🎉 批量修复完成！")
    print(f"   修复文件数: {fixed_files}")
    print(f"   总修复数: {total_fixes}")
    
    return fixed_files, total_fixes

def create_csp_helper_script():
    """创建 CSP 辅助脚本"""
    print("\n📝 创建 CSP 辅助脚本...")
    
    helper_script = '''/**
 * CSP 辅助脚本
 * 用于处理动态创建的脚本和样式
 */

(function() {
    'use strict';
    
    // 获取当前页面的 nonce
    const getCurrentNonce = () => {
        const scripts = document.querySelectorAll('script[nonce]');
        return scripts.length > 0 ? scripts[0].getAttribute('nonce') : '';
    };
    
    // 创建带 nonce 的脚本元素
    window.createScriptWithNonce = function(src, content) {
        const script = document.createElement('script');
        const nonce = getCurrentNonce();
        
        if (nonce) {
            script.setAttribute('nonce', nonce);
        }
        
        if (src) {
            script.src = src;
        }
        
        if (content) {
            script.textContent = content;
        }
        
        return script;
    };
    
    // 创建带 nonce 的样式元素
    window.createStyleWithNonce = function(content) {
        const style = document.createElement('style');
        const nonce = getCurrentNonce();
        
        if (nonce) {
            style.setAttribute('nonce', nonce);
        }
        
        if (content) {
            style.textContent = content;
        }
        
        return style;
    };
    
    // 安全地执行代码
    window.safeEval = function(code) {
        try {
            const script = createScriptWithNonce(null, code);
            document.head.appendChild(script);
            document.head.removeChild(script);
        } catch (e) {
            console.error('Safe eval failed:', e);
        }
    };
    
    console.log('✅ CSP 辅助脚本已加载');
})();'''
    
    # 保存辅助脚本
    helper_path = 'app/static/js/csp-helper.js'
    with open(helper_path, 'w', encoding='utf-8') as f:
        f.write(helper_script)
    
    print(f"✅ CSP 辅助脚本已保存到: {helper_path}")

def update_base_template():
    """更新基础模板以包含 CSP 辅助脚本"""
    print("\n🔧 更新基础模板...")
    
    base_template_path = 'app/templates/base.html'
    
    if not os.path.exists(base_template_path):
        print("⚠️ 基础模板不存在，跳过更新")
        return
    
    try:
        with open(base_template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含 CSP 辅助脚本
        if 'csp-helper.js' in content:
            print("✅ 基础模板已包含 CSP 辅助脚本")
            return
        
        # 在其他脚本之前添加 CSP 辅助脚本
        csp_helper_line = '    <script nonce="{{ csp_nonce }}" src="{{ url_for(\'static\', filename=\'js/csp-helper.js\') }}"></script>'
        
        # 查找合适的插入位置
        if '<!-- Scripts -->' in content:
            content = content.replace('<!-- Scripts -->', f'<!-- Scripts -->\n{csp_helper_line}')
        elif '<script' in content:
            # 在第一个 script 标签之前插入
            first_script_pos = content.find('<script')
            if first_script_pos != -1:
                content = content[:first_script_pos] + csp_helper_line + '\n    ' + content[first_script_pos:]
        else:
            # 在 </head> 之前插入
            head_end_pos = content.find('</head>')
            if head_end_pos != -1:
                content = content[:head_end_pos] + f'    {csp_helper_line}\n' + content[head_end_pos:]
        
        # 保存更新后的模板
        with open(base_template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 基础模板已更新")
        
    except Exception as e:
        print(f"❌ 更新基础模板失败: {e}")

def create_quick_fix_guide():
    """创建快速修复指南"""
    guide_content = '''# CSP 违规快速修复指南

## 🚀 自动修复

运行以下命令自动修复大部分 CSP 违规：

```bash
python fix_csp_violations.py
```

## 🔧 手动修复常见情况

### 1. 内联脚本
**修复前：**
```html
<script>
    console.log('Hello');
</script>
```

**修复后：**
```html
<script nonce="{{ csp_nonce }}">
    console.log('Hello');
</script>
```

### 2. 内联样式
**修复前：**
```html
<style>
    .custom { color: red; }
</style>
```

**修复后：**
```html
<style nonce="{{ csp_nonce }}">
    .custom { color: red; }
</style>
```

### 3. 事件处理器
**修复前：**
```html
<button onclick="doSomething()">Click</button>
```

**修复后：**
```html
<button id="myButton">Click</button>
<script nonce="{{ csp_nonce }}">
    document.getElementById('myButton').addEventListener('click', doSomething);
</script>
```

### 4. 动态脚本创建
**修复前：**
```javascript
const script = document.createElement('script');
script.textContent = 'console.log("test")';
document.head.appendChild(script);
```

**修复后：**
```javascript
const script = createScriptWithNonce(null, 'console.log("test")');
document.head.appendChild(script);
```

## 🛠️ 使用 CSP 辅助工具

加载 CSP 辅助脚本后，可以使用以下函数：

- `createScriptWithNonce(src, content)` - 创建带 nonce 的脚本
- `createStyleWithNonce(content)` - 创建带 nonce 的样式
- `safeEval(code)` - 安全执行代码

## 📋 检查清单

- [ ] 所有 `<script>` 标签都有 nonce 或 src
- [ ] 所有 `<style>` 标签都有 nonce
- [ ] 移除所有内联事件处理器（onclick 等）
- [ ] 动态创建的脚本使用辅助函数
- [ ] 测试页面功能正常

## 🔍 验证修复

在浏览器控制台检查是否还有 CSP 错误：
1. 打开开发者工具
2. 查看 Console 面板
3. 刷新页面
4. 确认没有 CSP 违规错误
'''
    
    with open('CSP修复指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 快速修复指南已保存到: CSP修复指南.md")

if __name__ == '__main__':
    print("🚀 CSP 违规批量修复工具")
    print("=" * 50)
    
    # 1. 批量修复模板文件
    fixed_files, total_fixes = fix_csp_violations()
    
    # 2. 创建 CSP 辅助脚本
    create_csp_helper_script()
    
    # 3. 更新基础模板
    update_base_template()
    
    # 4. 创建修复指南
    create_quick_fix_guide()
    
    print("\n" + "=" * 50)
    print("🎉 批量修复完成！")
    print(f"✅ 修复了 {fixed_files} 个文件，共 {total_fixes} 处修改")
    print("\n📝 后续步骤：")
    print("1. 重启应用服务器")
    print("2. 刷新浏览器页面")
    print("3. 检查控制台是否还有 CSP 错误")
    print("4. 查看 'CSP修复指南.md' 了解手动修复方法")
    print("\n💡 提示：如果还有错误，请查看指南进行手动修复")
