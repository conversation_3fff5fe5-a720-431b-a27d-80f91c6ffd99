#!/usr/bin/env python3
"""
检查后台管理功能访问权限
确保轮播图和在线咨询管理功能可以正常访问
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, Role
import json

def check_admin_permissions():
    """检查管理员权限配置"""
    print("🔍 检查管理员权限配置...")
    
    app = create_app()
    with app.app_context():
        try:
            # 查找管理员角色
            admin_roles = Role.query.filter(
                db.or_(
                    Role.name.like('%管理员%'),
                    Role.name.like('%admin%')
                )
            ).all()
            
            if not admin_roles:
                print("❌ 未找到管理员角色")
                return False
            
            print(f"✅ 找到 {len(admin_roles)} 个管理员角色")
            
            for role in admin_roles:
                print(f"\n📋 角色: {role.name} (ID: {role.id})")
                
                # 解析权限
                try:
                    permissions = json.loads(role.permissions or '{}')
                except json.JSONDecodeError:
                    permissions = {}
                
                # 检查轮播图权限
                carousel_perms = permissions.get('carousel', [])
                if carousel_perms:
                    print(f"  ✅ 轮播图权限: {carousel_perms}")
                else:
                    print("  ❌ 缺少轮播图权限")
                
                # 检查在线咨询权限
                consultation_perms = permissions.get('consultation', [])
                if consultation_perms:
                    print(f"  ✅ 在线咨询权限: {consultation_perms}")
                else:
                    print("  ❌ 缺少在线咨询权限")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查权限时出错: {str(e)}")
            return False

def add_missing_permissions():
    """添加缺失的权限"""
    print("\n🔧 添加缺失的权限...")
    
    app = create_app()
    with app.app_context():
        try:
            # 查找管理员角色
            admin_roles = Role.query.filter(
                db.or_(
                    Role.name.like('%管理员%'),
                    Role.name.like('%admin%')
                )
            ).all()
            
            for role in admin_roles:
                print(f"\n📝 更新角色: {role.name}")
                
                # 解析现有权限
                try:
                    permissions = json.loads(role.permissions or '{}')
                except json.JSONDecodeError:
                    permissions = {}
                
                # 添加轮播图权限
                if 'carousel' not in permissions:
                    permissions['carousel'] = ['view', 'create', 'edit', 'delete', 'toggle']
                    print("  ✅ 添加轮播图权限")
                
                # 添加在线咨询权限
                if 'consultation' not in permissions:
                    permissions['consultation'] = ['view', 'reply', 'close', 'export']
                    print("  ✅ 添加在线咨询权限")
                
                # 更新权限
                role.permissions = json.dumps(permissions, ensure_ascii=False)
                db.session.commit()
                print("  ✅ 权限更新完成")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加权限时出错: {str(e)}")
            db.session.rollback()
            return False

def check_routes():
    """检查路由是否正确注册"""
    print("\n🛣️  检查路由注册...")
    
    app = create_app()
    with app.app_context():
        try:
            # 检查轮播图路由
            carousel_routes = []
            consultation_routes = []
            
            for rule in app.url_map.iter_rules():
                if 'carousel' in rule.rule:
                    carousel_routes.append(rule.rule)
                elif 'consultation' in rule.rule:
                    consultation_routes.append(rule.rule)
            
            print(f"✅ 轮播图相关路由 ({len(carousel_routes)} 个):")
            for route in carousel_routes[:5]:  # 只显示前5个
                print(f"  - {route}")
            
            print(f"✅ 在线咨询相关路由 ({len(consultation_routes)} 个):")
            for route in consultation_routes[:5]:  # 只显示前5个
                print(f"  - {route}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查路由时出错: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 后台管理功能访问检查工具")
    print("=" * 60)
    
    # 1. 检查权限
    if not check_admin_permissions():
        print("\n🔧 尝试添加缺失的权限...")
        if not add_missing_permissions():
            print("❌ 权限配置失败")
            return False
    
    # 2. 检查路由
    if not check_routes():
        print("❌ 路由检查失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 检查完成！")
    print("=" * 60)
    
    print("\n📋 后台管理功能访问地址：")
    print("1. 首页轮播图管理:")
    print("   http://127.0.0.1:5000/admin/carousel")
    print("   - 查看轮播图列表")
    print("   - 添加/编辑/删除轮播图")
    print("   - 批量上传轮播图")
    
    print("\n2. 在线咨询管理:")
    print("   http://127.0.0.1:5000/consultation/list")
    print("   - 查看咨询列表")
    print("   - 回复和管理咨询")
    print("   - 搜索和筛选咨询")
    
    print("\n💡 使用说明：")
    print("1. 确保您使用管理员账号登录")
    print("2. 如果菜单中没有显示，请直接访问上述URL")
    print("3. 检查用户角色是否具有管理员权限")
    
    return True

if __name__ == '__main__':
    main()
