#!/usr/bin/env python3
"""
测试在线咨询功能修复
验证按照README.md最佳实践修改后的代码是否正常工作
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models_consultation import OnlineConsultation

def test_consultation_fix():
    """测试在线咨询功能修复"""
    app = create_app()

    with app.app_context():
        try:
            print("🧪 测试在线咨询功能修复...")
            print("=" * 50)

            # 测试创建咨询记录
            print("📝 测试创建咨询记录...")
            consultation = OnlineConsultation.create_consultation(
                name="测试用户",
                contact_type="微信",
                contact_value="test123",
                content="这是一个测试咨询内容，用于验证修复后的功能是否正常工作。",
                ip_address="127.0.0.1",
                user_agent="Test User Agent"
            )

            print(f"✅ 创建成功！咨询ID: {consultation.id}")
            print(f"   姓名: {consultation.name}")
            print(f"   联系方式: {consultation.contact_type} - {consultation.contact_value}")
            print(f"   状态: {consultation.status}")
            print(f"   创建时间: {consultation.created_at}")
            print(f"   更新时间: {consultation.updated_at}")

            # 测试回复功能
            print("\n💬 测试回复功能...")
            consultation.reply("这是一个测试回复内容。", 1)  # 假设用户ID为1

            print(f"✅ 回复成功！")

            # 重新查询以获取最新数据
            updated_consultation = OnlineConsultation.query.get(consultation.id)
            print(f"   回复内容: {updated_consultation.reply_content}")
            print(f"   回复时间: {updated_consultation.reply_time}")
            print(f"   状态: {updated_consultation.status}")

            # 测试关闭功能
            print("\n🔒 测试关闭功能...")
            updated_consultation.close()

            print(f"✅ 关闭成功！")

            # 再次重新查询以获取最新数据
            final_consultation = OnlineConsultation.query.get(consultation.id)
            print(f"   状态: {final_consultation.status}")

            # 测试查询功能
            print("\n🔍 测试查询功能...")
            pending_count = OnlineConsultation.get_pending_count()
            print(f"✅ 待处理咨询数量: {pending_count}")

            # 测试统计功能
            print("\n📊 测试统计功能...")
            stats = OnlineConsultation.get_statistics()
            print(f"✅ 统计信息获取成功")
            print(f"   状态统计: {stats.get('status_stats', {})}")
            print(f"   联系方式统计: {stats.get('contact_type_stats', {})}")

            print("\n🎉 所有测试通过！在线咨询功能修复成功！")
            return True

        except Exception as e:
            print(f"\n❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = test_consultation_fix()
    if success:
        print("\n✅ 修复验证成功！可以正常使用在线咨询功能了。")
    else:
        print("\n❌ 修复验证失败！请检查错误信息并进行进一步修复。")
