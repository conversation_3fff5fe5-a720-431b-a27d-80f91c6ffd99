"""
测试应用程序

此脚本用于测试创建应用程序，以便找出问题所在。
"""
import sys
import traceback
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_create_app():
    """测试创建应用程序"""
    try:
        from app import create_app
        print("✓ 成功导入create_app函数")
        
        app = create_app()
        print("✓ 成功创建应用程序")
        
        return app
    except Exception as e:
        print(f"✗ 创建应用程序失败: {e}")
        traceback.print_exc()
        return None

def test_db_connection(app):
    """测试数据库连接"""
    if not app:
        print("✗ 无法测试数据库连接，因为应用程序创建失败")
        return False
    
    try:
        from app import db
        print("✓ 成功导入db对象")
        
        with app.app_context():
            db.engine.connect()
            print("✓ 成功连接到数据库")
        
        return True
    except Exception as e:
        print(f"✗ 连接数据库失败: {e}")
        traceback.print_exc()
        return False

def test_model_creation(app):
    """测试模型创建"""
    if not app:
        print("✗ 无法测试模型创建，因为应用程序创建失败")
        return False
    
    try:
        from app.models import User
        print("✓ 成功导入User模型")
        
        with app.app_context():
            user = User(username="test_user", email="<EMAIL>")
            print("✓ 成功创建User对象")
            print(f"  - user.username: {user.username}")
            print(f"  - user.email: {user.email}")
        
        return True
    except Exception as e:
        print(f"✗ 创建模型失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*30 + "测试应用程序")
    print("="*80)
    
    # 测试创建应用程序
    app = test_create_app()
    
    # 测试数据库连接
    if app:
        test_db_connection(app)
        test_model_creation(app)
    
    print("\n" + "="*80)

if __name__ == "__main__":
    main()
