#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
食材导入工具测试脚本
用于验证导入工具的各项功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from PIL import Image

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory, AdministrativeArea
    from tools.ingredient_batch_importer import IngredientBatchImporter
    from tools.quick_ingredient_importer import QuickIngredientImporter
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class IngredientImporterTester:
    """食材导入工具测试器"""

    def __init__(self):
        self.test_folder = Path("test_ingredient_data")
        self.test_images = []

    def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")

        # 创建测试文件夹
        if self.test_folder.exists():
            shutil.rmtree(self.test_folder)

        self.test_folder.mkdir(parents=True)

        # 创建分类文件夹
        categories = ["蔬菜", "肉类", "水果", "调料"]
        for category in categories:
            (self.test_folder / category).mkdir()

        print(f"✅ 测试文件夹创建完成: {self.test_folder}")

    def create_test_images(self):
        """创建测试图片"""
        print("🖼️ 创建测试图片...")

        test_ingredients = [
            {"name": "西红柿", "category": "蔬菜", "color": (255, 100, 100)},
            {"name": "黄瓜", "category": "蔬菜", "color": (100, 255, 100)},
            {"name": "胡萝卜", "category": "蔬菜", "color": (255, 165, 0)},
            {"name": "猪肉", "category": "肉类", "color": (255, 192, 203)},
            {"name": "牛肉", "category": "肉类", "color": (139, 69, 19)},
            {"name": "苹果", "category": "水果", "color": (255, 0, 0)},
            {"name": "香蕉", "category": "水果", "color": (255, 255, 0)},
            {"name": "盐", "category": "调料", "color": (255, 255, 255)},
            {"name": "胡椒粉", "category": "调料", "color": (64, 64, 64)}
        ]

        for ingredient in test_ingredients:
            # 创建彩色图片
            img = Image.new('RGB', (800, 600), ingredient["color"])

            # 保存到对应分类文件夹
            category_folder = self.test_folder / ingredient["category"]
            image_path = category_folder / f"{ingredient['name']}.jpg"
            img.save(image_path, 'JPEG', quality=95)

            self.test_images.append({
                'path': image_path,
                'name': ingredient['name'],
                'category': ingredient['category']
            })

            print(f"  ✅ 创建图片: {ingredient['name']}.jpg")

        print(f"✅ 共创建 {len(self.test_images)} 个测试图片")

    def test_batch_importer(self):
        """测试批量导入器"""
        print("\n🧪 测试批量导入器...")

        try:
            # 创建导入器
            importer = IngredientBatchImporter(
                source_folder=str(self.test_folder),
                target_folder=Path(project_root) / "app" / "static" / "uploads" / "test_ingredients"
            )

            # 检查前提条件
            if not importer.check_prerequisites():
                print("❌ 前提条件检查失败")
                return False

            # 扫描文件夹
            scan_result = importer.scan_source_folder()

            print(f"📊 扫描结果:")
            print(f"  总文件数: {scan_result['total_files']}")
            print(f"  图片文件: {len(scan_result['image_files'])}")
            print(f"  发现分类: {len(scan_result['categories'])}")

            # 执行导入（测试模式，小批次）
            report = importer.batch_import(scan_result, area_id=None, batch_size=5)

            # 显示报告
            importer.print_import_report(report)

            # 如果所有文件都被跳过（重复），这也算成功
            total_processed = report['summary']['ingredients_imported'] + report['summary']['duplicates_skipped']
            expected_files = len(scan_result['image_files'])

            success = (total_processed == expected_files and report['summary']['errors_occurred'] == 0)

            return success

        except Exception as e:
            print(f"❌ 批量导入测试失败: {e}")
            return False

    def test_quick_importer(self):
        """测试快速导入器"""
        print("\n🧪 测试快速导入器...")

        try:
            # 创建快速导入器
            importer = QuickIngredientImporter(source_folder=str(self.test_folder))

            # 测试预定义食材导入
            print("测试预定义食材导入...")
            importer.import_predefined_ingredients()

            # 如果有导入成功或者全部跳过（重复），都算成功
            success = (importer.imported_count > 0) or (importer.skipped_count > 0 and importer.error_count == 0)
            print(f"✅ 快速导入测试{'成功' if success else '失败'}")

            return success

        except Exception as e:
            print(f"❌ 快速导入测试失败: {e}")
            return False

    def test_image_compression(self):
        """测试图片压缩功能"""
        print("\n🧪 测试图片压缩...")

        try:
            from tools.ingredient_batch_importer import IngredientBatchImporter

            importer = IngredientBatchImporter()

            # 选择一个测试图片
            if not self.test_images:
                print("❌ 没有测试图片")
                return False

            source_path = self.test_images[0]['path']
            target_path = Path(tempfile.gettempdir()) / "test_compressed.jpg"

            # 获取原始大小
            original_size = source_path.stat().st_size

            # 压缩图片
            success = importer.compress_image(source_path, target_path)

            if success and target_path.exists():
                compressed_size = target_path.stat().st_size
                compression_ratio = (1 - compressed_size / original_size) * 100

                print(f"✅ 图片压缩成功:")
                print(f"  原始大小: {original_size:,} 字节")
                print(f"  压缩大小: {compressed_size:,} 字节")
                print(f"  压缩比: {compression_ratio:.1f}%")

                # 清理临时文件
                target_path.unlink()

                return True
            else:
                print("❌ 图片压缩失败")
                return False

        except Exception as e:
            print(f"❌ 图片压缩测试失败: {e}")
            return False

    def test_duplicate_detection(self):
        """测试重复检测功能"""
        print("\n🧪 测试重复检测...")

        try:
            from tools.ingredient_batch_importer import IngredientBatchImporter

            importer = IngredientBatchImporter()

            # 使用原始SQL创建测试食材（避免DATETIME2精度问题）
            sql = text("""
                INSERT INTO ingredients
                (name, category, unit, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :unit, :status, GETDATE(), GETDATE())
            """)

            result = db.session.execute(sql, {
                'name': "测试食材_重复检测",
                'category': "测试分类",
                'unit': "份",
                'status': 1
            })

            test_ingredient_id = result.fetchone()[0]
            db.session.commit()

            # 检测重复
            duplicate = importer.check_duplicate_ingredient("测试食材_重复检测")

            if duplicate:
                print("✅ 重复检测功能正常")

                # 清理测试数据
                cleanup_sql = text("DELETE FROM ingredients WHERE id = :id")
                db.session.execute(cleanup_sql, {'id': test_ingredient_id})
                db.session.commit()

                return True
            else:
                print("❌ 重复检测功能异常")
                # 清理测试数据
                cleanup_sql = text("DELETE FROM ingredients WHERE id = :id")
                db.session.execute(cleanup_sql, {'id': test_ingredient_id})
                db.session.commit()
                return False

        except Exception as e:
            print(f"❌ 重复检测测试失败: {e}")
            return False

    def cleanup_test_environment(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")

        try:
            # 删除测试文件夹
            if self.test_folder.exists():
                shutil.rmtree(self.test_folder)
                print("✅ 测试文件夹已删除")

            # 清理测试数据库记录
            test_ingredients = Ingredient.query.filter(
                Ingredient.name.like('%测试%')
            ).all()

            for ingredient in test_ingredients:
                db.session.delete(ingredient)

            db.session.commit()
            print(f"✅ 清理了 {len(test_ingredients)} 个测试食材记录")

        except Exception as e:
            print(f"⚠️ 清理过程中出现错误: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 **食材导入工具测试套件**")
        print("="*50)

        test_results = []

        try:
            # 设置测试环境
            self.setup_test_environment()
            self.create_test_images()

            # 运行各项测试
            tests = [
                ("图片压缩功能", self.test_image_compression),
                ("重复检测功能", self.test_duplicate_detection),
                ("快速导入器", self.test_quick_importer),
                ("批量导入器", self.test_batch_importer)
            ]

            for test_name, test_func in tests:
                print(f"\n{'='*20} {test_name} {'='*20}")
                try:
                    result = test_func()
                    test_results.append((test_name, result))
                    print(f"{'✅ 通过' if result else '❌ 失败'}: {test_name}")
                except Exception as e:
                    test_results.append((test_name, False))
                    print(f"❌ 异常: {test_name} - {e}")

            # 显示测试总结
            print(f"\n{'='*50}")
            print("📊 **测试总结**")
            print(f"{'='*50}")

            passed = sum(1 for _, result in test_results if result)
            total = len(test_results)

            for test_name, result in test_results:
                status = "✅ 通过" if result else "❌ 失败"
                print(f"  {status}: {test_name}")

            print(f"\n📈 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")

            if passed == total:
                print("🎉 所有测试通过！工具可以正常使用。")
            else:
                print("⚠️ 部分测试失败，请检查相关功能。")

        finally:
            # 清理测试环境
            self.cleanup_test_environment()

def main():
    """主函数"""
    app = create_app()

    with app.app_context():
        tester = IngredientImporterTester()
        tester.run_all_tests()

if __name__ == "__main__":
    main()
