#!/usr/bin/env python3
"""
修复数据库连接问题
解决SQL Server连接占线错误
"""

from app import create_app, db
from sqlalchemy import text
import logging

def fix_database_connections():
    """修复数据库连接配置"""
    print("🔧 修复数据库连接配置...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查当前连接池状态
            print("📊 检查数据库连接池状态...")
            engine = db.engine
            pool = engine.pool
            
            print(f"  连接池大小: {pool.size()}")
            print(f"  当前连接数: {pool.checkedin()}")
            print(f"  已检出连接: {pool.checkedout()}")
            print(f"  无效连接数: {pool.invalidated()}")
            
            # 2. 测试基本连接
            print("\n🔍 测试数据库连接...")
            test_sql = text("SELECT 1 as test")
            result = db.session.execute(test_sql).fetchone()
            if result and result.test == 1:
                print("✅ 数据库连接正常")
            else:
                print("❌ 数据库连接异常")
                return False
            
            # 3. 检查轮播图表
            print("\n📋 检查轮播图表...")
            table_sql = text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'homepage_carousel'
            """)
            table_result = db.session.execute(table_sql).fetchone()
            
            if table_result.count > 0:
                print("✅ homepage_carousel 表存在")
                
                # 检查表数据
                data_sql = text("SELECT COUNT(*) as count FROM homepage_carousel")
                data_result = db.session.execute(data_sql).fetchone()
                print(f"📊 表中有 {data_result.count} 条轮播图数据")
            else:
                print("❌ homepage_carousel 表不存在")
                return False
            
            # 4. 检查用户表关联
            print("\n👤 检查用户表关联...")
            user_sql = text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'administrative_areas'
            """)
            user_result = db.session.execute(user_sql).fetchone()
            
            if user_result.count > 0:
                print("✅ administrative_areas 表存在")
            else:
                print("❌ administrative_areas 表不存在")
            
            # 5. 清理可能的死锁连接
            print("\n🧹 清理数据库连接...")
            try:
                # 关闭所有连接
                db.session.close()
                # 清理连接池
                engine.dispose()
                print("✅ 数据库连接已清理")
            except Exception as e:
                print(f"⚠️  清理连接时出现警告: {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 修复数据库连接失败: {str(e)}")
            return False

def optimize_database_config():
    """优化数据库配置"""
    print("\n⚙️  优化数据库配置建议...")
    print("-" * 50)
    
    print("📋 建议的数据库配置优化:")
    print("1. 增加连接池大小:")
    print("   SQLALCHEMY_ENGINE_OPTIONS = {")
    print("       'pool_size': 20,")
    print("       'pool_timeout': 30,")
    print("       'pool_recycle': 3600,")
    print("       'max_overflow': 30")
    print("   }")
    
    print("\n2. 启用连接池预检:")
    print("   SQLALCHEMY_ENGINE_OPTIONS = {")
    print("       'pool_pre_ping': True")
    print("   }")
    
    print("\n3. 设置连接超时:")
    print("   SQLALCHEMY_ENGINE_OPTIONS = {")
    print("       'connect_args': {")
    print("           'timeout': 30,")
    print("           'autocommit': True")
    print("       }")
    print("   }")

def create_database_config_file():
    """创建数据库配置文件"""
    print("\n📝 创建优化的数据库配置...")
    print("-" * 50)
    
    config_content = '''"""
数据库连接优化配置
解决SQL Server连接占线问题
"""

# 数据库连接池配置
SQLALCHEMY_ENGINE_OPTIONS = {
    # 连接池大小
    'pool_size': 20,
    
    # 连接池溢出大小
    'max_overflow': 30,
    
    # 连接超时时间（秒）
    'pool_timeout': 30,
    
    # 连接回收时间（秒）
    'pool_recycle': 3600,
    
    # 连接预检（防止连接断开）
    'pool_pre_ping': True,
    
    # SQL Server 特定配置
    'connect_args': {
        'timeout': 30,
        'autocommit': False,
        'fast_executemany': True
    }
}

# 数据库连接字符串优化
# 添加连接池和超时参数
DATABASE_URL_PARAMS = {
    'Connection Timeout': 30,
    'Command Timeout': 30,
    'Pooling': 'true',
    'Max Pool Size': 100,
    'Min Pool Size': 5,
    'Connection Lifetime': 3600
}
'''
    
    try:
        with open('database_config_optimized.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ 配置文件已创建: database_config_optimized.py")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {str(e)}")
        return False

def test_carousel_operations():
    """测试轮播图操作"""
    print("\n🧪 测试轮播图操作...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 测试查询操作
            print("📋 测试查询操作...")
            sql = text("""
                SELECT id, title, description, image_path, link_url, sort_order, is_active, created_at, updated_at
                FROM homepage_carousel
                ORDER BY sort_order ASC, created_at DESC
            """)
            
            result = db.session.execute(sql)
            carousels = []
            
            for row in result:
                carousels.append({
                    'id': row.id,
                    'title': row.title,
                    'description': row.description,
                    'image_path': row.image_path,
                    'link_url': row.link_url,
                    'sort_order': row.sort_order,
                    'is_active': row.is_active,
                    'created_at': row.created_at.strftime('%Y-%m-%d %H:%M') if row.created_at else '',
                    'updated_at': row.updated_at.strftime('%Y-%m-%d %H:%M') if row.updated_at else ''
                })
            
            print(f"✅ 查询成功，获取到 {len(carousels)} 条轮播图数据")
            
            # 测试API查询
            print("\n📡 测试API查询...")
            api_sql = text("""
                SELECT id, title, description, image_path, link_url, sort_order
                FROM homepage_carousel
                WHERE is_active = 1
                ORDER BY sort_order ASC, created_at DESC
            """)
            
            api_result = db.session.execute(api_sql)
            api_carousels = []
            
            for row in api_result:
                api_carousels.append({
                    'id': row.id,
                    'title': row.title,
                    'description': row.description,
                    'image_path': row.image_path,
                    'link_url': row.link_url,
                    'sort_order': row.sort_order
                })
            
            print(f"✅ API查询成功，获取到 {len(api_carousels)} 条启用的轮播图")
            
            # 确保会话正确关闭
            db.session.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 测试轮播图操作失败: {str(e)}")
            db.session.rollback()
            return False

def main():
    """主修复函数"""
    print("🔧 数据库连接问题修复工具")
    print("=" * 60)
    print("解决SQL Server连接占线错误")
    print("=" * 60)
    
    # 执行修复步骤
    steps = [
        ("修复数据库连接", fix_database_connections()),
        ("测试轮播图操作", test_carousel_operations()),
        ("创建优化配置", create_database_config_file()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 修复结果")
    print("=" * 60)
    
    for step_name, result in steps:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{step_name:<15}: {status}")
    
    passed_steps = sum(1 for _, result in steps if result)
    total_steps = len(steps)
    
    print(f"\n📊 总体结果: {passed_steps}/{total_steps} 步骤成功")
    
    # 显示优化建议
    optimize_database_config()
    
    if passed_steps == total_steps:
        print("\n🎉 数据库连接问题修复完成！")
        print("\n✅ 修复内容:")
        print("1. ✅ 清理了数据库连接池")
        print("2. ✅ 验证了轮播图操作")
        print("3. ✅ 创建了优化配置文件")
        
        print("\n📋 下一步操作:")
        print("1. 重启应用服务器")
        print("2. 应用优化的数据库配置")
        print("3. 监控连接池状态")
        print("4. 测试轮播图功能")
        
        print("\n💡 预防措施:")
        print("1. 确保所有数据库操作后正确关闭会话")
        print("2. 使用连接池预检防止连接断开")
        print("3. 设置合理的连接超时时间")
        print("4. 定期监控数据库连接状态")
        
    else:
        print("\n⚠️  部分修复失败")
        print("\n🔧 建议操作:")
        print("1. 检查数据库服务器状态")
        print("2. 确认数据库连接字符串正确")
        print("3. 重启数据库服务")
        print("4. 检查防火墙设置")
        print("5. 联系数据库管理员")
    
    return passed_steps == total_steps

if __name__ == '__main__':
    main()
