#!/usr/bin/env python3
"""
测试默认链接修复功能
验证批量上传和单张上传页面的默认链接设置
"""

import requests
from bs4 import BeautifulSoup
import re

def test_batch_upload_default_link():
    """测试批量上传页面的默认链接"""
    print("📤 测试批量上传页面默认链接...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问批量上传页面
        print("📄 访问批量上传页面...")
        response = requests.get(f"{base_url}/admin/carousel/batch-upload", timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # 检查默认链接输入框
            link_input = soup.find('input', {'id': 'default_link_url'})
            if link_input:
                default_value = link_input.get('value', '')
                if default_value == '/':
                    print("✅ 默认链接值正确设置为 '/'")
                else:
                    print(f"❌ 默认链接值错误: '{default_value}'")
                    return False
                
                # 检查输入框类型
                input_type = link_input.get('type', '')
                if input_type == 'text':
                    print("✅ 输入框类型已修改为 text")
                else:
                    print(f"⚠️  输入框类型: {input_type}")
            else:
                print("❌ 未找到默认链接输入框")
                return False
            
            # 检查快速设置按钮
            quick_buttons = soup.find_all('button', string=re.compile(r'首页|登录|注册'))
            if len(quick_buttons) >= 3:
                print(f"✅ 找到 {len(quick_buttons)} 个快速设置按钮")
                
                # 检查首页按钮是否高亮
                home_button = soup.find('button', string=re.compile(r'首页'))
                if home_button and 'btn-outline-primary' in home_button.get('class', []):
                    print("✅ 首页按钮已高亮显示")
                else:
                    print("⚠️  首页按钮未高亮")
            else:
                print(f"❌ 快速设置按钮数量不足: {len(quick_buttons)}")
                return False
            
            # 检查JavaScript功能
            js_checks = [
                ("DOMContentLoaded", "页面加载事件"),
                ("setDefaultLink", "设置默认链接函数"),
                ("highlightActiveButton", "按钮高亮函数"),
                ("resetForm", "重置表单函数"),
            ]
            
            for js_check, description in js_checks:
                if js_check in content:
                    print(f"  ✅ {description} 存在")
                else:
                    print(f"  ❌ {description} 缺失")
                    return False
            
            return True
            
        elif response.status_code in [302, 301]:
            print("⚠️  页面重定向到登录页面（需要登录）")
            return True  # 重定向是正常的
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_single_upload_default_link():
    """测试单张上传页面的默认链接"""
    print("\n📝 测试单张上传页面默认链接...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问单张上传页面
        print("📄 访问单张上传页面...")
        response = requests.get(f"{base_url}/admin/carousel/create", timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # 检查链接输入框
            link_input = soup.find('input', {'id': 'link_url'})
            if link_input:
                # 检查模板中的默认值设置
                if "carousel.link_url if carousel else '/'" in content:
                    print("✅ 模板默认值设置正确")
                else:
                    print("❌ 模板默认值设置错误")
                    return False
                
                # 检查输入框类型
                input_type = link_input.get('type', '')
                if input_type == 'text':
                    print("✅ 输入框类型已修改为 text")
                else:
                    print(f"⚠️  输入框类型: {input_type}")
            else:
                print("❌ 未找到链接输入框")
                return False
            
            # 检查快速设置按钮
            quick_buttons = soup.find_all('button', string=re.compile(r'设为首页|设为登录|设为注册'))
            if len(quick_buttons) >= 3:
                print(f"✅ 找到 {len(quick_buttons)} 个快速设置按钮")
                
                # 检查按钮图标
                home_button = soup.find('button', string=re.compile(r'设为首页'))
                if home_button and 'fa-home' in str(home_button):
                    print("✅ 首页按钮包含图标")
                else:
                    print("⚠️  首页按钮缺少图标")
            else:
                print(f"❌ 快速设置按钮数量不足: {len(quick_buttons)}")
                return False
            
            # 检查JavaScript功能
            js_checks = [
                ("DOMContentLoaded", "页面加载事件"),
                ("if not carousel", "新建页面判断"),
                ("setDefaultLink", "设置默认链接函数"),
                ("highlightActiveButton", "按钮高亮函数"),
            ]
            
            for js_check, description in js_checks:
                if js_check in content:
                    print(f"  ✅ {description} 存在")
                else:
                    print(f"  ❌ {description} 缺失")
                    return False
            
            return True
            
        elif response.status_code in [302, 301]:
            print("⚠️  页面重定向到登录页面（需要登录）")
            return True  # 重定向是正常的
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_carousel_list_page():
    """测试轮播图列表页面"""
    print("\n📋 测试轮播图列表页面...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问列表页面
        print("📄 访问轮播图列表页面...")
        response = requests.get(f"{base_url}/admin/carousel", timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # 检查页面链接
            links_to_check = [
                ("/admin/carousel/create", "添加轮播图"),
                ("/admin/carousel/batch-upload", "批量上传"),
            ]
            
            results = []
            for link_url, description in links_to_check:
                link = soup.find('a', href=link_url)
                if link:
                    print(f"  ✅ {description} 链接存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 链接缺失")
                    results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 列表页面检查: {success_count}/{total_count} 通过")
            return success_count == total_count
            
        elif response.status_code in [302, 301]:
            print("⚠️  页面重定向到登录页面（需要登录）")
            return True  # 重定向是正常的
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 默认链接修复功能测试")
    print("=" * 60)
    print("测试批量上传和单张上传页面的默认链接设置")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("轮播图列表页面", test_carousel_list_page()),
        ("批量上传默认链接", test_batch_upload_default_link()),
        ("单张上传默认链接", test_single_upload_default_link()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 修复测试结果")
    print("=" * 60)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 默认链接修复成功！")
        print("\n✅ 修复内容确认:")
        print("1. ✅ 批量上传页面默认链接设置为 '/'")
        print("2. ✅ 单张上传页面默认链接设置为 '/'")
        print("3. ✅ 输入框类型改为 text（避免URL验证问题）")
        print("4. ✅ 快速设置按钮添加图标和高亮效果")
        print("5. ✅ 页面加载时自动设置默认值")
        print("6. ✅ 重置表单时恢复默认值")
        
        print("\n📋 现在的使用体验:")
        print("1. 访问批量上传页面，默认链接已填入 '/'")
        print("2. 访问单张上传页面，默认链接已填入 '/'")
        print("3. 点击快速设置按钮，立即设置常用链接")
        print("4. 首页按钮默认高亮显示")
        print("5. 重置表单后自动恢复默认值")
        
        print("\n💡 使用建议:")
        print("1. 大部分情况下保持默认的 '/' 链接")
        print("2. 如需跳转到特定页面，使用快速设置按钮")
        print("3. 自定义链接时，输入完整的URL路径")
        
    else:
        print("\n⚠️  部分修复测试失败")
        print("\n🔧 建议操作:")
        
        if not tests[0][1]:  # 列表页面
            print("1. 检查轮播图管理页面路由")
            print("2. 确认权限配置正确")
        
        if not tests[1][1]:  # 批量上传
            print("3. 检查批量上传页面模板")
            print("4. 确认JavaScript代码正确")
        
        if not tests[2][1]:  # 单张上传
            print("5. 检查单张上传页面模板")
            print("6. 确认默认值设置正确")
        
        print("\n7. 清除浏览器缓存")
        print("8. 重启应用服务器")
        print("9. 再次运行此测试")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
