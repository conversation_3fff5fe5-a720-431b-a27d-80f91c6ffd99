#!/usr/bin/env python3
"""
测试轮播图增删改查功能
验证所有CRUD操作是否正常工作
"""

import sys
import os
import requests
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from app import create_app, db
        from app.models_homepage_carousel import HomepageCarousel
        
        app = create_app()
        with app.app_context():
            # 测试查询
            count = db.session.execute(db.text("SELECT COUNT(*) FROM homepage_carousel")).scalar()
            print(f"✅ 数据库连接正常，当前有 {count} 个轮播图")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def test_routes():
    """测试路由是否正确注册"""
    print("\n🛣️  测试路由注册...")
    
    try:
        from app import create_app
        
        app = create_app()
        with app.app_context():
            carousel_routes = []
            
            for rule in app.url_map.iter_rules():
                if 'carousel' in rule.rule:
                    carousel_routes.append({
                        'rule': rule.rule,
                        'methods': list(rule.methods),
                        'endpoint': rule.endpoint
                    })
            
            print(f"✅ 找到 {len(carousel_routes)} 个轮播图相关路由:")
            
            expected_routes = [
                '/admin/carousel',
                '/admin/carousel/create',
                '/admin/carousel/batch-upload',
                '/admin/carousel/<int:id>/edit',
                '/admin/carousel/<int:id>/delete',
                '/admin/carousel/<int:id>/toggle',
                '/admin/carousel/<int:id>/sort',
                '/admin/carousel/batch-delete'
            ]
            
            found_routes = [route['rule'] for route in carousel_routes]
            
            for expected in expected_routes:
                if any(expected.replace('<int:id>', str(1)) in route or expected in route for route in found_routes):
                    print(f"  ✅ {expected}")
                else:
                    print(f"  ❌ {expected} - 未找到")
            
            return True
            
    except Exception as e:
        print(f"❌ 路由测试失败: {str(e)}")
        return False

def test_crud_operations():
    """测试CRUD操作"""
    print("\n📋 测试CRUD操作...")
    
    try:
        from app import create_app, db
        from app.models_homepage_carousel import HomepageCarousel
        from sqlalchemy import text
        
        app = create_app()
        with app.app_context():
            
            # 1. 测试创建 (Create)
            print("  📝 测试创建操作...")
            test_data = {
                'title': '测试轮播图',
                'description': '这是一个测试轮播图',
                'image_path': '/static/test/test.jpg',
                'link_url': 'https://example.com',
                'sort_order': 1,
                'is_active': True,
                'created_by': 'test_user'
            }
            
            # 使用原始SQL插入
            insert_sql = text("""
                INSERT INTO homepage_carousel 
                (title, description, image_path, link_url, sort_order, is_active, created_by, created_at, updated_at)
                VALUES (:title, :description, :image_path, :link_url, :sort_order, :is_active, :created_by, GETDATE(), GETDATE())
            """)
            
            result = db.session.execute(insert_sql, test_data)
            db.session.commit()
            print("    ✅ 创建操作成功")
            
            # 2. 测试读取 (Read)
            print("  📖 测试读取操作...")
            select_sql = text("SELECT * FROM homepage_carousel WHERE title = :title")
            carousel = db.session.execute(select_sql, {'title': '测试轮播图'}).fetchone()
            
            if carousel:
                print(f"    ✅ 读取操作成功，ID: {carousel.id}")
                test_id = carousel.id
            else:
                print("    ❌ 读取操作失败")
                return False
            
            # 3. 测试更新 (Update)
            print("  ✏️  测试更新操作...")
            update_sql = text("""
                UPDATE homepage_carousel 
                SET title = :new_title, updated_at = GETDATE()
                WHERE id = :id
            """)
            
            db.session.execute(update_sql, {
                'new_title': '更新后的测试轮播图',
                'id': test_id
            })
            db.session.commit()
            
            # 验证更新
            updated_carousel = db.session.execute(
                text("SELECT title FROM homepage_carousel WHERE id = :id"),
                {'id': test_id}
            ).fetchone()
            
            if updated_carousel and updated_carousel.title == '更新后的测试轮播图':
                print("    ✅ 更新操作成功")
            else:
                print("    ❌ 更新操作失败")
            
            # 4. 测试删除 (Delete)
            print("  🗑️  测试删除操作...")
            delete_sql = text("DELETE FROM homepage_carousel WHERE id = :id")
            db.session.execute(delete_sql, {'id': test_id})
            db.session.commit()
            
            # 验证删除
            deleted_carousel = db.session.execute(
                text("SELECT * FROM homepage_carousel WHERE id = :id"),
                {'id': test_id}
            ).fetchone()
            
            if not deleted_carousel:
                print("    ✅ 删除操作成功")
            else:
                print("    ❌ 删除操作失败")
            
            return True
            
    except Exception as e:
        print(f"❌ CRUD操作测试失败: {str(e)}")
        return False

def test_web_interface():
    """测试Web界面"""
    print("\n🌐 测试Web界面...")
    
    try:
        # 测试列表页面
        response = requests.get('http://127.0.0.1:5000/admin/carousel', timeout=5)
        if response.status_code == 200:
            print("  ✅ 列表页面访问正常")
        else:
            print(f"  ❌ 列表页面访问失败，状态码: {response.status_code}")
            
        # 测试创建页面
        response = requests.get('http://127.0.0.1:5000/admin/carousel/create', timeout=5)
        if response.status_code == 200:
            print("  ✅ 创建页面访问正常")
        else:
            print(f"  ❌ 创建页面访问失败，状态码: {response.status_code}")
            
        # 测试批量上传页面
        response = requests.get('http://127.0.0.1:5000/admin/carousel/batch-upload', timeout=5)
        if response.status_code == 200:
            print("  ✅ 批量上传页面访问正常")
        else:
            print(f"  ❌ 批量上传页面访问失败，状态码: {response.status_code}")
            
        return True
        
    except requests.exceptions.RequestException:
        print("  ❌ 应用未运行，请先启动应用: python run.py")
        return False

def main():
    """主函数"""
    print("🎠 轮播图增删改查功能测试")
    print("=" * 60)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("\n❌ 数据库连接测试失败，请检查数据库配置")
        return False
    
    # 2. 测试路由注册
    if not test_routes():
        print("\n❌ 路由测试失败")
        return False
    
    # 3. 测试CRUD操作
    if not test_crud_operations():
        print("\n❌ CRUD操作测试失败")
        return False
    
    # 4. 测试Web界面
    if not test_web_interface():
        print("\n❌ Web界面测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 轮播图增删改查功能测试完成！")
    print("=" * 60)
    
    print("\n📋 功能清单:")
    print("✅ 增加 (Create):")
    print("   - 单张轮播图添加")
    print("   - 批量轮播图上传")
    print("   - 自动图片压缩 (1920x800)")
    
    print("\n✅ 删除 (Delete):")
    print("   - 单个轮播图删除")
    print("   - 批量轮播图删除")
    print("   - 自动删除图片文件")
    
    print("\n✅ 修改 (Update):")
    print("   - 编辑轮播图信息")
    print("   - 启用/禁用状态切换")
    print("   - 排序调整 (上移/下移)")
    
    print("\n✅ 查询 (Read):")
    print("   - 轮播图列表显示")
    print("   - 图片预览功能")
    print("   - 状态和排序显示")
    
    print("\n🔗 访问地址:")
    print("📍 轮播图管理: http://127.0.0.1:5000/admin/carousel")
    print("📍 添加轮播图: http://127.0.0.1:5000/admin/carousel/create")
    print("📍 批量上传: http://127.0.0.1:5000/admin/carousel/batch-upload")
    
    return True

if __name__ == '__main__':
    main()
