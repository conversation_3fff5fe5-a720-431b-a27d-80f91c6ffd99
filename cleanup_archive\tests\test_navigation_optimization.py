#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导航菜单优雅化测试脚本
验证菜单优化功能是否正确实现
"""

import os
import sys
import json

def test_file_exists(file_path, description):
    """测试文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - 文件不存在")
        return False

def test_menu_config():
    """测试菜单配置"""
    try:
        # 测试优雅菜单配置
        sys.path.append('.')
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG, get_elegant_user_menu
        
        print("✅ 优雅菜单配置加载成功")
        
        # 检查菜单结构
        if len(ELEGANT_MENU_CONFIG) == 5:
            print(f"✅ 优雅菜单主项数量正确: {len(ELEGANT_MENU_CONFIG)}个")
        else:
            print(f"⚠️ 优雅菜单主项数量: {len(ELEGANT_MENU_CONFIG)}个 (期望5个)")
        
        # 检查菜单项名称
        expected_menus = ['工作台', '菜单规划', '供应链', '质量安全', '系统']
        actual_menus = [item['name'] for item in ELEGANT_MENU_CONFIG]
        
        for expected in expected_menus:
            if expected in actual_menus:
                print(f"  ✅ 找到菜单项: {expected}")
            else:
                print(f"  ❌ 缺少菜单项: {expected}")
        
        # 检查子菜单数量
        total_children = 0
        for item in ELEGANT_MENU_CONFIG:
            if 'children' in item:
                children_count = len(item['children'])
                total_children += children_count
                print(f"  📋 {item['name']}: {children_count}个子项")
        
        print(f"✅ 总子菜单项: {total_children}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 优雅菜单配置测试失败: {str(e)}")
        return False

def test_api_routes():
    """测试API路由"""
    try:
        with open('app/main/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查API路由
        if '/api/menu/elegant' in content:
            print("✅ 优雅菜单API路由存在")
        else:
            print("❌ 优雅菜单API路由缺失")
            
        if '/api/menu/classic' in content:
            print("✅ 经典菜单API路由存在")
        else:
            print("❌ 经典菜单API路由缺失")
            
        if 'get_elegant_user_menu' in content:
            print("✅ 优雅菜单函数调用存在")
        else:
            print("❌ 优雅菜单函数调用缺失")
            
        return True
        
    except Exception as e:
        print(f"❌ API路由测试失败: {str(e)}")
        return False

def test_css_content():
    """测试CSS样式内容"""
    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键样式
        key_styles = [
            'backdrop-filter',
            'dropdown-menu',
            'dropdown-item',
            'elegant-menu',
            '@media (max-width: 768px)'
        ]
        
        for style in key_styles:
            if style in content:
                print(f"  ✅ 包含样式: {style}")
            else:
                print(f"  ❌ 缺少样式: {style}")
                
        return True
        
    except Exception as e:
        print(f"❌ CSS样式测试失败: {str(e)}")
        return False

def test_js_functionality():
    """测试JavaScript功能"""
    try:
        with open('app/static/js/menu-switcher.js', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键功能
        key_functions = [
            'class MenuSwitcher',
            'switchMode',
            'loadElegantMenu',
            'loadClassicMenu',
            'previewMenu',
            'localStorage'
        ]
        
        for func in key_functions:
            if func in content:
                print(f"  ✅ 包含功能: {func}")
            else:
                print(f"  ❌ 缺少功能: {func}")
                
        return True
        
    except Exception as e:
        print(f"❌ JavaScript功能测试失败: {str(e)}")
        return False

def test_template_integration():
    """测试模板集成"""
    try:
        with open('app/templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'menu-switcher.js' in content:
            print("✅ 菜单切换器已集成到基础模板")
        else:
            print("❌ 菜单切换器未集成到基础模板")
            
        return True
        
    except Exception as e:
        print(f"❌ 模板集成测试失败: {str(e)}")
        return False

def analyze_menu_optimization():
    """分析菜单优化效果"""
    try:
        # 分析原有菜单
        from app.utils.menu import MENU_CONFIG
        original_count = len(MENU_CONFIG)
        
        # 分析优雅菜单
        from app.utils.menu_elegant import ELEGANT_MENU_CONFIG
        elegant_count = len(ELEGANT_MENU_CONFIG)
        
        # 计算优化效果
        reduction = ((original_count - elegant_count) / original_count) * 100
        
        print(f"\n📊 菜单优化效果分析:")
        print(f"  原有主菜单: {original_count}个")
        print(f"  优雅主菜单: {elegant_count}个")
        print(f"  减少数量: {original_count - elegant_count}个")
        print(f"  优化程度: {reduction:.1f}%")
        
        if reduction >= 50:
            print("  🎉 优化效果显著!")
        elif reduction >= 30:
            print("  ✅ 优化效果良好!")
        else:
            print("  ⚠️ 优化效果一般")
            
        return True
        
    except Exception as e:
        print(f"❌ 菜单优化分析失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试导航菜单优雅化功能...")
    print("=" * 60)
    
    test_results = []
    
    # 测试文件存在性
    print("\n📁 测试文件存在性:")
    test_results.append(test_file_exists(
        'app/utils/menu_elegant.py',
        '优雅菜单配置文件'
    ))
    
    test_results.append(test_file_exists(
        'app/static/css/elegant-navigation.css',
        '优雅导航样式文件'
    ))
    
    test_results.append(test_file_exists(
        'app/static/js/menu-switcher.js',
        '菜单切换器脚本'
    ))
    
    test_results.append(test_file_exists(
        'NAVIGATION_OPTIMIZATION_GUIDE.md',
        '导航优化指南文档'
    ))
    
    # 测试菜单配置
    print("\n📋 测试菜单配置:")
    test_results.append(test_menu_config())
    
    # 测试API路由
    print("\n🛣️ 测试API路由:")
    test_results.append(test_api_routes())
    
    # 测试CSS样式
    print("\n🎨 测试CSS样式:")
    test_results.append(test_css_content())
    
    # 测试JavaScript功能
    print("\n⚡ 测试JavaScript功能:")
    test_results.append(test_js_functionality())
    
    # 测试模板集成
    print("\n🌐 测试模板集成:")
    test_results.append(test_template_integration())
    
    # 分析优化效果
    print("\n📈 分析优化效果:")
    test_results.append(analyze_menu_optimization())
    
    # 统计结果
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！导航菜单优雅化功能已正确实现。")
        print("\n🚀 使用说明:")
        print("1. 启动应用后，导航栏会出现菜单切换按钮 📋")
        print("2. 点击按钮可以在优雅模式和经典模式之间切换")
        print("3. 使用 Ctrl+M 快捷键快速切换菜单模式")
        print("4. 点击'预览效果'可以临时体验另一种模式")
        print("5. 用户的选择会自动保存，下次访问时生效")
        return True
    else:
        print("⚠️ 部分测试未通过，请检查相关文件。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
