"""添加库存与出入库模块的溯源功能相关表和字段

Revision ID: d9cf6329bc27
Revises: ec19dce5184b
Create Date: 2025-05-05 18:45:22.904578

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd9cf6329bc27'
down_revision = 'ec19dce5184b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('warehouses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('area_id', sa.Integer(), nullable=False),
    sa.Column('location', sa.String(length=200), nullable=False),
    sa.Column('manager_id', sa.Integer(), nullable=False),
    sa.Column('capacity', sa.Float(), nullable=True),
    sa.Column('capacity_unit', sa.String(length=20), nullable=True),
    sa.Column('temperature_range', sa.String(length=50), nullable=True),
    sa.Column('humidity_range', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=20), server_default='正常', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['area_id'], ['administrative_areas.id'], ),
    sa.ForeignKeyConstraint(['manager_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('inventory_checks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('check_number', sa.String(length=50), nullable=False),
    sa.Column('warehouse_id', sa.Integer(), nullable=False),
    sa.Column('check_date', sa.Date(), nullable=False),
    sa.Column('check_type', sa.String(length=20), nullable=False),
    sa.Column('operator_id', sa.Integer(), nullable=False),
    sa.Column('approver_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), server_default='待盘点', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['approver_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['operator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('check_number')
    )
    op.create_table('storage_locations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('warehouse_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('location_code', sa.String(length=50), nullable=False),
    sa.Column('storage_type', sa.String(length=50), nullable=False),
    sa.Column('capacity', sa.Float(), nullable=True),
    sa.Column('capacity_unit', sa.String(length=20), nullable=True),
    sa.Column('temperature_range', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=20), server_default='正常', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('inventories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('warehouse_id', sa.Integer(), nullable=False),
    sa.Column('storage_location_id', sa.Integer(), nullable=False),
    sa.Column('ingredient_id', sa.Integer(), nullable=False),
    sa.Column('batch_number', sa.String(length=50), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('production_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('supplier_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), server_default='正常', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['ingredient_id'], ['ingredients.id'], ),
    sa.ForeignKeyConstraint(['storage_location_id'], ['storage_locations.id'], ),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ),
    sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('stock_outs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('stock_out_number', sa.String(length=50), nullable=False),
    sa.Column('warehouse_id', sa.Integer(), nullable=False),
    sa.Column('consumption_plan_id', sa.Integer(), nullable=True),
    sa.Column('stock_out_date', sa.DateTime(), nullable=False),
    sa.Column('stock_out_type', sa.String(length=20), nullable=False),
    sa.Column('operator_id', sa.Integer(), nullable=False),
    sa.Column('approver_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), server_default='待审核', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['approver_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['consumption_plan_id'], ['consumption_plans.id'], ),
    sa.ForeignKeyConstraint(['operator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('stock_out_number')
    )
    op.create_table('inventory_check_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('check_id', sa.Integer(), nullable=False),
    sa.Column('inventory_id', sa.Integer(), nullable=False),
    sa.Column('system_quantity', sa.Float(), nullable=False),
    sa.Column('actual_quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('difference', sa.Float(), nullable=False),
    sa.Column('difference_reason', sa.String(length=200), nullable=True),
    sa.Column('adjustment_type', sa.String(length=20), nullable=True),
    sa.Column('is_adjusted', sa.Boolean(), server_default='0', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['check_id'], ['inventory_checks.id'], ),
    sa.ForeignKeyConstraint(['inventory_id'], ['inventories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('stock_ins',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('stock_in_number', sa.String(length=50), nullable=False),
    sa.Column('warehouse_id', sa.Integer(), nullable=False),
    sa.Column('delivery_id', sa.Integer(), nullable=True),
    sa.Column('stock_in_date', sa.DateTime(), nullable=False),
    sa.Column('stock_in_type', sa.String(length=20), nullable=False),
    sa.Column('operator_id', sa.Integer(), nullable=False),
    sa.Column('inspector_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), server_default='待审核', nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['delivery_id'], ['supplier_deliveries.id'], ),
    sa.ForeignKeyConstraint(['inspector_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['operator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['warehouse_id'], ['warehouses.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('stock_in_number')
    )
    op.create_table('stock_out_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('stock_out_id', sa.Integer(), nullable=False),
    sa.Column('inventory_id', sa.Integer(), nullable=False),
    sa.Column('ingredient_id', sa.Integer(), nullable=False),
    sa.Column('batch_number', sa.String(length=50), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('consumption_detail_id', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['consumption_detail_id'], ['consumption_details.id'], ),
    sa.ForeignKeyConstraint(['ingredient_id'], ['ingredients.id'], ),
    sa.ForeignKeyConstraint(['inventory_id'], ['inventories.id'], ),
    sa.ForeignKeyConstraint(['stock_out_id'], ['stock_outs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('stock_in_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('stock_in_id', sa.Integer(), nullable=False),
    sa.Column('ingredient_id', sa.Integer(), nullable=False),
    sa.Column('batch_number', sa.String(length=50), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit', sa.String(length=20), nullable=False),
    sa.Column('production_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('storage_location_id', sa.Integer(), nullable=False),
    sa.Column('supplier_id', sa.Integer(), nullable=True),
    sa.Column('quality_check_result', sa.String(length=20), nullable=True),
    sa.Column('quality_check_notes', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['ingredient_id'], ['ingredients.id'], ),
    sa.ForeignKeyConstraint(['stock_in_id'], ['stock_ins.id'], ),
    sa.ForeignKeyConstraint(['storage_location_id'], ['storage_locations.id'], ),
    sa.ForeignKeyConstraint(['supplier_id'], ['suppliers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('stock_in_items')
    op.drop_table('stock_out_items')
    op.drop_table('stock_ins')
    op.drop_table('inventory_check_items')
    op.drop_table('stock_outs')
    op.drop_table('inventories')
    op.drop_table('storage_locations')
    op.drop_table('inventory_checks')
    op.drop_table('warehouses')
    # ### end Alembic commands ###
