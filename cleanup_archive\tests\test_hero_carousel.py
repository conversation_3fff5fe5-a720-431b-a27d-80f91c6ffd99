#!/usr/bin/env python3
"""
测试英雄区域轮播图功能
验证首页轮播图替换是否成功
"""

import requests
from bs4 import BeautifulSoup
import json

def test_homepage_carousel_integration():
    """测试首页轮播图集成"""
    print("🏠 测试首页英雄区域轮播图集成...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问首页
        print("📄 访问首页...")
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # 检查英雄区域轮播图元素
            checks = [
                ("heroCarousel", "英雄区域轮播图容器"),
                ("heroCarouselIndicators", "轮播图指示器"),
                ("heroCarouselInner", "轮播图内容区域"),
                ("heroCarouselLoading", "加载状态"),
                ("heroCarouselEmpty", "空状态"),
                ("HeroCarousel", "轮播图JavaScript类"),
            ]
            
            results = []
            for check, description in checks:
                if check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查CSS样式
            css_checks = [
                ("#heroCarousel", "轮播图容器样式"),
                (".hero-indicators", "指示器样式"),
                (".hero-control", "控制按钮样式"),
                ("@media (max-width: 768px)", "响应式样式"),
            ]
            
            for css_check, description in css_checks:
                if css_check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查是否移除了原来的静态图片
            if 'picsum.photos/id/292/600/400' in content:
                # 检查是否在空状态中（这是正常的）
                empty_div = soup.find('div', {'id': 'heroCarouselEmpty'})
                if empty_div and 'picsum.photos/id/292/600/400' in str(empty_div):
                    print("  ✅ 原静态图片已移至空状态（正常）")
                    results.append(True)
                else:
                    print("  ⚠️  原静态图片仍在主要位置")
                    results.append(False)
            else:
                print("  ✅ 原静态图片已完全移除")
                results.append(True)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 首页轮播图集成检查: {success_count}/{total_count} 通过")
            return success_count >= total_count * 0.8  # 80%通过即可
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 首页测试失败: {str(e)}")
        return False

def test_carousel_api():
    """测试轮播图API"""
    print("\n📡 测试轮播图API...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试API端点
        response = requests.get(f"{base_url}/api/carousel/list", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API响应成功")
            
            if data.get('success'):
                carousels = data.get('data', [])
                print(f"✅ 获取到 {len(carousels)} 个轮播图")
                
                if carousels:
                    print("📋 轮播图列表:")
                    for i, carousel in enumerate(carousels[:3], 1):  # 只显示前3个
                        title = carousel.get('title', '无标题')
                        link = carousel.get('link_url', '/')
                        print(f"  {i}. {title} -> {link}")
                
                return True
            else:
                print(f"❌ API返回错误: {data.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_carousel_functionality():
    """测试轮播图功能完整性"""
    print("\n🔧 测试轮播图功能完整性...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问管理页面
        print("📄 测试管理页面访问...")
        response = requests.get(f"{base_url}/admin/carousel", timeout=10, allow_redirects=False)
        
        if response.status_code == 200:
            print("✅ 轮播图管理页面可访问")
        elif response.status_code in [302, 301]:
            print("⚠️  轮播图管理页面需要登录")
        else:
            print(f"❌ 轮播图管理页面访问失败: {response.status_code}")
            return False
        
        # 测试批量上传页面
        print("📄 测试批量上传页面...")
        response = requests.get(f"{base_url}/admin/carousel/batch-upload", timeout=10, allow_redirects=False)
        
        if response.status_code == 200:
            print("✅ 批量上传页面可访问")
        elif response.status_code in [302, 301]:
            print("⚠️  批量上传页面需要登录")
        else:
            print(f"❌ 批量上传页面访问失败: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {str(e)}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    print("\n📱 测试响应式设计...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 模拟移动端访问
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
        }
        
        response = requests.get(base_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查响应式相关的CSS
            responsive_checks = [
                ("@media (max-width: 768px)", "移动端媒体查询"),
                ("height: 250px", "移动端轮播图高度"),
                ("width: 35px", "移动端控制按钮大小"),
            ]
            
            results = []
            for check, description in responsive_checks:
                if check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 响应式设计检查: {success_count}/{total_count} 通过")
            return success_count == total_count
        else:
            print(f"❌ 移动端访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 响应式测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🎠 英雄区域轮播图功能测试")
    print("=" * 60)
    print("测试首页静态图片替换为轮播图")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("轮播图API", test_carousel_api()),
        ("首页集成", test_homepage_carousel_integration()),
        ("功能完整性", test_carousel_functionality()),
        ("响应式设计", test_responsive_design()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 英雄区域轮播图替换成功！")
        print("\n✨ 功能特性:")
        print("1. ✅ 静态图片已替换为动态轮播图")
        print("2. ✅ 自动从后台轮播图数据加载")
        print("3. ✅ 支持点击跳转功能")
        print("4. ✅ 响应式设计，适配移动端")
        print("5. ✅ 优雅的加载和空状态处理")
        print("6. ✅ 4秒自动切换间隔")
        
        print("\n📋 使用效果:")
        print("1. 首页英雄区域现在显示轮播图")
        print("2. 如果有轮播图数据，自动轮播展示")
        print("3. 如果没有数据，显示默认图片")
        print("4. 管理员可在后台管理轮播图内容")
        
        print("\n🎯 管理建议:")
        print("1. 上传适合的系统界面截图")
        print("2. 设置合适的标题和描述")
        print("3. 配置点击跳转链接")
        print("4. 定期更新轮播图内容")
        
    else:
        print("\n⚠️  部分测试失败")
        print("\n🔧 建议操作:")
        
        if not tests[0][1]:  # API测试
            print("1. 检查轮播图API是否正常")
            print("2. 确认数据库中有轮播图数据")
        
        if not tests[1][1]:  # 首页集成
            print("3. 检查首页模板是否正确更新")
            print("4. 清除浏览器缓存")
        
        if not tests[2][1]:  # 功能完整性
            print("5. 检查管理页面路由")
            print("6. 确认权限配置")
        
        if not tests[3][1]:  # 响应式设计
            print("7. 检查CSS媒体查询")
            print("8. 测试移动端显示效果")
        
        print("\n9. 重启应用服务器")
        print("10. 再次运行此测试")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
