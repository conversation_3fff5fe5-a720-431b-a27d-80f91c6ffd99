#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
菜单宽度优化测试脚本
验证下拉菜单宽度是否已经优化
"""

import os
import sys

def test_css_width_optimization():
    """测试CSS宽度优化"""
    print("🎨 测试CSS宽度优化...")
    
    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # 检查关键的宽度优化设置
        optimizations = [
            ('min-width: 200px', '最小宽度设置为200px'),
            ('max-width: 250px', '最大宽度设置为250px'),
            ('width: auto', '自动宽度'),
            ('white-space: nowrap', '防止文本换行'),
            ('width: fit-content', '内容适应宽度'),
            ('text-overflow: ellipsis', '文本溢出省略号'),
            ('overflow: hidden', '隐藏溢出内容')
        ]
        
        results = []
        for setting, description in optimizations:
            if setting in css_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ CSS测试失败: {str(e)}")
        return False

def test_padding_optimization():
    """测试内边距优化"""
    print("\n📏 测试内边距优化...")
    
    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # 检查内边距优化
        padding_checks = [
            ('padding: 8px 16px', '菜单项内边距优化'),
            ('margin: 1px 6px', '菜单项外边距优化'),
            ('font-size: 13px', '字体大小优化'),
            ('width: 16px', '图标宽度优化'),
            ('margin-right: 8px', '图标间距优化')
        ]
        
        results = []
        for check, description in padding_checks:
            if check in css_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 内边距测试失败: {str(e)}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    print("\n📱 测试响应式设计...")
    
    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # 检查响应式设计
        responsive_checks = [
            ('@media (max-width: 768px)', '移动端媒体查询'),
            ('min-width: 180px', '紧凑模式最小宽度'),
            ('max-width: 220px', '紧凑模式最大宽度'),
            ('padding: 6px 12px', '紧凑模式内边距')
        ]
        
        results = []
        for check, description in responsive_checks:
            if check in css_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 响应式设计测试失败: {str(e)}")
        return False

def calculate_width_reduction():
    """计算宽度减少程度"""
    print("\n📊 计算宽度优化效果...")
    
    try:
        # 原始宽度假设为280px，优化后为200-250px
        original_width = 280
        optimized_min_width = 200
        optimized_max_width = 250
        
        min_reduction = ((original_width - optimized_max_width) / original_width) * 100
        max_reduction = ((original_width - optimized_min_width) / original_width) * 100
        
        print(f"  📈 原始最小宽度: {original_width}px")
        print(f"  📉 优化后宽度范围: {optimized_min_width}px - {optimized_max_width}px")
        print(f"  📊 宽度减少: {min_reduction:.1f}% - {max_reduction:.1f}%")
        
        if min_reduction >= 10:
            print("  🎉 宽度优化效果显著!")
            return True
        else:
            print("  ⚠️ 宽度优化效果一般")
            return False
            
    except Exception as e:
        print(f"❌ 宽度计算失败: {str(e)}")
        return False

def generate_optimization_summary():
    """生成优化总结"""
    print("\n📋 菜单宽度优化总结:")
    print("-" * 50)
    
    optimizations = [
        "🎯 最小宽度从280px减少到200px",
        "📏 最大宽度限制为250px",
        "🔧 使用width: auto和fit-content自适应",
        "📝 菜单项内边距从10px减少到8px",
        "🖼️ 图标宽度从18px减少到16px",
        "📱 添加紧凑模式支持",
        "✂️ 长文本自动截断处理",
        "🎨 保持视觉美观和功能完整"
    ]
    
    for optimization in optimizations:
        print(f"  {optimization}")
    
    print("\n💡 优化效果:")
    print("  • 菜单更加紧凑，不会占用过多屏幕空间")
    print("  • 保持良好的可读性和可点击性")
    print("  • 适应不同长度的菜单项文本")
    print("  • 在移动端和桌面端都有良好表现")

def main():
    """主测试函数"""
    print("🔍 开始测试菜单宽度优化...")
    print("=" * 60)
    
    test_results = []
    
    # 测试CSS宽度优化
    test_results.append(test_css_width_optimization())
    
    # 测试内边距优化
    test_results.append(test_padding_optimization())
    
    # 测试响应式设计
    test_results.append(test_responsive_design())
    
    # 计算宽度减少程度
    test_results.append(calculate_width_reduction())
    
    # 统计结果
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"📊 测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 菜单宽度优化成功！")
        generate_optimization_summary()
        
        print("\n🚀 现在菜单宽度更加合适，用户体验更好！")
        print("🌐 刷新页面查看优化效果")
        return True
    else:
        print("⚠️ 部分优化未完成，请检查CSS文件。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
