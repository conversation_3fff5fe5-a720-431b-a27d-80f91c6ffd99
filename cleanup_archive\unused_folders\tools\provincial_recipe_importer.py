#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
省厅食谱智能导入工具
解析省厅食谱.MD文件，将食谱和食材信息导入到系统中
"""

import os
import sys
import re
import uuid
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Recipe, RecipeCategory, Ingredient, RecipeIngredient
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class ProvincialRecipeImporter:
    def __init__(self, md_file_path: str = "省厅食谱.MD"):
        """
        初始化省厅食谱导入器

        Args:
            md_file_path: MD文件路径
        """
        self.md_file_path = Path(md_file_path)

        # 统计信息
        self.stats = {
            'total_recipes': 0,
            'created_recipes': 0,
            'updated_recipes': 0,
            'total_ingredients': 0,
            'matched_ingredients': 0,
            'unmatched_ingredients': 0,
            'created_ingredient_relations': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }

        self.errors = []
        self.unmatched_ingredients = []
        self.created_recipes = []

        # 食材名称映射表（处理不同的命名方式）
        self.ingredient_mapping = {
            '稻米': '大米',
            '玉米糁(黄)': '玉米糁',
            '玉米糁': '玉米糁',
            '白薯叶': '红薯叶',
            '辣椒(青,尖)': '青椒',
            '辣椒': '青椒',
            '甜椒': '彩椒',
            '彩椒': '彩椒',
            '小白菜': '上海青',
            '芹菜茎': '芹菜',
            '白花菜': '花菜',
            '番茄': '西红柿',
            '蛋': '鸡蛋',
            '洋葱': '洋葱',
            '苋菜': '红苋菜',
            '生菜': '生菜',
            '蒜苔': '蒜苔',
            '大白菜': '白菜',
            '红薯': '红薯',
            '黄瓜': '黄瓜',
            '猪肝': '猪肝',
            '杏鲍菇': '杏鲍菇',
            '包菜': '卷心菜',
            '红薯叶': '红薯叶',
            '虾': '基围虾',
            '油': '食用油'
        }

    def parse_md_file(self) -> List[Dict]:
        """
        解析MD文件，提取食谱信息

        Returns:
            List[Dict]: 食谱列表
        """
        print(f"📖 解析文件: {self.md_file_path}")

        if not self.md_file_path.exists():
            raise FileNotFoundError(f"文件不存在: {self.md_file_path}")

        with open(self.md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        recipes = []
        current_week = None
        current_day = None
        current_recipe = None

        lines = content.split('\n')

        for line in lines:
            line = line.strip()
            if not line or line.startswith('---'):
                continue

            # 解析周次
            week_match = re.match(r'###\s*\*\*第(\d+)周\*\*', line)
            if week_match:
                current_week = int(week_match.group(1))
                continue

            # 解析日期
            day_match = re.match(r'####\s*\*\*周([一二三四五六日])\*\*', line)
            if day_match:
                day_mapping = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '日': 7}
                current_day = day_mapping[day_match.group(1)]
                continue

            # 解析菜品
            recipe_match = re.match(r'-\s*\*\*([^*]+)\*\*', line)
            if recipe_match:
                recipe_name = recipe_match.group(1).strip()
                current_recipe = {
                    'name': recipe_name,
                    'week': current_week,
                    'day': current_day,
                    'ingredients': [],
                    'category': self.determine_category(recipe_name),
                    'meal_type': '午餐',
                    'description': f'湖南省农村义务教育学生营养午餐食谱 - 第{current_week}周周{["", "一", "二", "三", "四", "五", "六", "日"][current_day]}'
                }
                recipes.append(current_recipe)
                continue

            # 解析食材
            ingredient_match = re.match(r'\s*-\s*([^：:]+)[：:]\s*(.+)', line)
            if ingredient_match and current_recipe:
                ingredient_name = ingredient_match.group(1).strip()
                amounts_text = ingredient_match.group(2).strip()

                # 解析不同年级的用量
                amounts = self.parse_amounts(amounts_text)

                current_recipe['ingredients'].append({
                    'name': ingredient_name,
                    'amounts': amounts,
                    'unit': self.determine_unit(ingredient_name, amounts_text)
                })

        self.stats['total_recipes'] = len(recipes)
        print(f"📊 解析完成，共找到 {len(recipes)} 个食谱")

        return recipes

    def determine_category(self, recipe_name: str) -> str:
        """
        根据食谱名称确定分类

        Args:
            recipe_name: 食谱名称

        Returns:
            str: 分类名称
        """
        if '饭' in recipe_name:
            return '主食'
        elif any(meat in recipe_name for meat in ['鸡', '鸭', '肉', '牛', '猪', '虾']):
            return '荤菜'
        elif any(veg in recipe_name for veg in ['菜', '豆', '萝卜', '西红柿', '黄瓜', '洋葱']):
            return '素菜'
        else:
            return '其他'

    def parse_amounts(self, amounts_text: str) -> Dict[str, float]:
        """
        解析不同年级的用量

        Args:
            amounts_text: 用量文本

        Returns:
            Dict[str, float]: 年级用量映射
        """
        amounts = {}

        # 匹配模式：1-3年级XXg，4-6年级XXg，7-9年级XXg
        pattern = r'(\d+-\d+年级)(\d+(?:\.\d+)?)g'
        matches = re.findall(pattern, amounts_text)

        for grade_range, amount in matches:
            amounts[grade_range] = float(amount)

        # 如果没有匹配到，尝试简单的数字匹配
        if not amounts:
            simple_match = re.search(r'(\d+(?:\.\d+)?)g', amounts_text)
            if simple_match:
                amount = float(simple_match.group(1))
                amounts['通用'] = amount

        return amounts

    def determine_unit(self, ingredient_name: str, amounts_text: str) -> str:
        """
        确定食材单位

        Args:
            ingredient_name: 食材名称
            amounts_text: 用量文本

        Returns:
            str: 单位
        """
        if 'g' in amounts_text:
            return 'g'
        elif 'ml' in amounts_text:
            return 'ml'
        elif '个' in amounts_text:
            return '个'
        else:
            return 'g'  # 默认单位

    def find_or_create_category(self, category_name: str) -> int:
        """
        查找或创建食谱分类

        Args:
            category_name: 分类名称

        Returns:
            int: 分类ID
        """
        category = RecipeCategory.query.filter_by(name=category_name).first()
        if not category:
            category = RecipeCategory(
                name=category_name,
                description=f'省厅食谱自动创建的{category_name}分类'
            )
            db.session.add(category)
            db.session.flush()

        return category.id

    def find_ingredient_by_name(self, ingredient_name: str) -> Optional[Ingredient]:
        """
        根据名称查找食材（支持模糊匹配和映射）

        Args:
            ingredient_name: 食材名称

        Returns:
            Optional[Ingredient]: 找到的食材对象
        """
        # 首先尝试映射表
        mapped_name = self.ingredient_mapping.get(ingredient_name, ingredient_name)

        # 精确匹配
        ingredient = Ingredient.query.filter_by(name=mapped_name).first()
        if ingredient:
            return ingredient

        # 模糊匹配
        ingredient = Ingredient.query.filter(Ingredient.name.like(f'%{mapped_name}%')).first()
        if ingredient:
            return ingredient

        # 尝试原始名称
        if mapped_name != ingredient_name:
            ingredient = Ingredient.query.filter_by(name=ingredient_name).first()
            if ingredient:
                return ingredient

            ingredient = Ingredient.query.filter(Ingredient.name.like(f'%{ingredient_name}%')).first()
            if ingredient:
                return ingredient

        return None

    def calculate_average_amount(self, amounts: Dict[str, float]) -> float:
        """
        计算平均用量

        Args:
            amounts: 年级用量映射

        Returns:
            float: 平均用量
        """
        if not amounts:
            return 0.0

        if '通用' in amounts:
            return amounts['通用']

        # 计算所有年级的平均值
        total = sum(amounts.values())
        count = len(amounts)
        return round(total / count, 1) if count > 0 else 0.0

    def import_recipe(self, recipe_data: Dict) -> bool:
        """
        导入单个食谱

        Args:
            recipe_data: 食谱数据

        Returns:
            bool: 是否成功
        """
        try:
            recipe_name = recipe_data['name']

            # 检查食谱是否已存在
            existing_recipe = Recipe.query.filter_by(name=recipe_name).first()

            if existing_recipe:
                print(f"⏭️ 食谱已存在，跳过: {recipe_name}")
                self.stats['updated_recipes'] += 1
                return True

            # 获取或创建分类
            category_id = self.find_or_create_category(recipe_data['category'])

            # 使用原始SQL创建食谱
            sql = text("""
                INSERT INTO recipes
                (name, category, category_id, meal_type, description, status, is_global, is_user_defined, priority, created_by, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :category_id, :meal_type, :description, :status, :is_global, :is_user_defined, :priority, :created_by, GETDATE(), GETDATE())
            """)

            params = {
                'name': recipe_name,
                'category': recipe_data['category'],
                'category_id': category_id,
                'meal_type': recipe_data['meal_type'],
                'description': recipe_data['description'],
                'status': 1,  # 启用
                'is_global': True,  # 设为全局食谱
                'is_user_defined': False,  # 不是用户自定义
                'priority': 100,  # 高优先级
                'created_by': 1  # 系统用户
            }

            result = db.session.execute(sql, params)
            recipe_id = result.fetchone()[0]

            # 处理食材关联
            ingredient_count = 0
            for ingredient_data in recipe_data['ingredients']:
                if self.add_recipe_ingredient(recipe_id, ingredient_data):
                    ingredient_count += 1

            self.stats['created_recipes'] += 1
            self.created_recipes.append({
                'id': recipe_id,
                'name': recipe_name,
                'category': recipe_data['category'],
                'ingredient_count': ingredient_count
            })

            print(f"✅ 创建食谱: {recipe_name} (ID: {recipe_id}, 食材: {ingredient_count}个)")
            return True

        except Exception as e:
            error_msg = f"创建食谱失败 {recipe_name}: {e}"
            self.errors.append(error_msg)
            self.stats['errors'] += 1
            print(f"❌ {error_msg}")
            return False

    def add_recipe_ingredient(self, recipe_id: int, ingredient_data: Dict) -> bool:
        """
        添加食谱食材关联

        Args:
            recipe_id: 食谱ID
            ingredient_data: 食材数据

        Returns:
            bool: 是否成功
        """
        try:
            ingredient_name = ingredient_data['name']

            # 查找食材
            ingredient = self.find_ingredient_by_name(ingredient_name)

            if not ingredient:
                self.unmatched_ingredients.append(ingredient_name)
                self.stats['unmatched_ingredients'] += 1
                print(f"⚠️ 未找到食材: {ingredient_name}")
                return False

            # 计算平均用量
            average_amount = self.calculate_average_amount(ingredient_data['amounts'])
            unit = ingredient_data['unit']

            # 检查是否已存在关联
            existing = RecipeIngredient.query.filter_by(
                recipe_id=recipe_id,
                ingredient_id=ingredient.id
            ).first()

            if existing:
                # 更新用量
                existing.quantity = average_amount
                existing.unit = unit
            else:
                # 创建新关联
                recipe_ingredient = RecipeIngredient(
                    recipe_id=recipe_id,
                    ingredient_id=ingredient.id,
                    quantity=average_amount,
                    unit=unit
                )
                db.session.add(recipe_ingredient)

            self.stats['matched_ingredients'] += 1
            self.stats['created_ingredient_relations'] += 1

            return True

        except Exception as e:
            print(f"❌ 添加食材关联失败 {ingredient_name}: {e}")
            return False

    def batch_import(self, recipes: List[Dict], batch_size: int = 10) -> Dict:
        """
        批量导入食谱

        Args:
            recipes: 食谱列表
            batch_size: 批次大小

        Returns:
            Dict: 导入报告
        """
        self.stats['start_time'] = datetime.now()

        total_recipes = len(recipes)
        print(f"🚀 开始批量导入 {total_recipes} 个食谱...")

        # 分批处理
        batch_count = (total_recipes + batch_size - 1) // batch_size

        for batch_num in range(batch_count):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_recipes)
            batch_recipes = recipes[start_idx:end_idx]

            print(f"\n📦 处理批次 {batch_num + 1}/{batch_count} ({len(batch_recipes)} 个食谱)")

            for i, recipe_data in enumerate(batch_recipes):
                progress = ((batch_num * batch_size + i + 1) / total_recipes) * 100
                print(f"[{progress:.1f}%] ", end="")

                self.import_recipe(recipe_data)

            # 提交当前批次
            try:
                db.session.commit()
                print(f"✅ 批次 {batch_num + 1} 提交成功")
            except Exception as e:
                db.session.rollback()
                error_msg = f"批次 {batch_num + 1} 提交失败: {e}"
                self.errors.append(error_msg)
                print(f"❌ {error_msg}")

        self.stats['end_time'] = datetime.now()

        # 生成报告
        return self.generate_report()

    def generate_report(self) -> Dict:
        """生成导入报告"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']

        report = {
            'summary': {
                'total_recipes_found': self.stats['total_recipes'],
                'recipes_created': self.stats['created_recipes'],
                'recipes_updated': self.stats['updated_recipes'],
                'total_ingredients_processed': self.stats['total_ingredients'],
                'ingredients_matched': self.stats['matched_ingredients'],
                'ingredients_unmatched': self.stats['unmatched_ingredients'],
                'ingredient_relations_created': self.stats['created_ingredient_relations'],
                'errors_occurred': self.stats['errors'],
                'duration_seconds': duration.total_seconds() if duration else None,
                'success_rate': (self.stats['created_recipes'] / max(self.stats['total_recipes'], 1)) * 100
            },
            'created_recipes': self.created_recipes,
            'unmatched_ingredients': list(set(self.unmatched_ingredients)),
            'errors': self.errors,
            'timestamp': datetime.now().isoformat()
        }

        # 保存报告到文件
        report_filename = f"provincial_recipe_import_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            report['report_file'] = report_filename
        except Exception as e:
            print(f"⚠️ 保存报告文件失败: {e}")

        return report

    def print_import_report(self, report: Dict):
        """打印导入报告"""
        print("\n" + "="*60)
        print("🏛️ **省厅食谱导入报告**")
        print("="*60)

        summary = report['summary']

        print(f"📊 导入统计:")
        print(f"  📖 解析食谱总数: {summary['total_recipes_found']}")
        print(f"  ✅ 成功创建食谱: {summary['recipes_created']}")
        print(f"  🔄 更新食谱数量: {summary['recipes_updated']}")
        print(f"  🥘 处理食材总数: {summary['total_ingredients_processed']}")
        print(f"  ✅ 匹配食材数量: {summary['ingredients_matched']}")
        print(f"  ❌ 未匹配食材数: {summary['ingredients_unmatched']}")
        print(f"  🔗 创建食材关联: {summary['ingredient_relations_created']}")
        print(f"  ❌ 处理错误数量: {summary['errors_occurred']}")
        print(f"  📈 成功率: {summary['success_rate']:.1f}%")

        if summary['duration_seconds']:
            print(f"  ⏱️ 总耗时: {summary['duration_seconds']:.2f} 秒")

        # 显示创建的食谱
        if report['created_recipes']:
            print(f"\n📋 **创建的食谱** ({len(report['created_recipes'])} 个):")
            for recipe in report['created_recipes'][:10]:
                print(f"  ✅ {recipe['name']} ({recipe['category']}) - {recipe['ingredient_count']}个食材")
            if len(report['created_recipes']) > 10:
                print(f"  ... 还有 {len(report['created_recipes']) - 10} 个食谱")

        # 显示未匹配的食材
        if report['unmatched_ingredients']:
            print(f"\n⚠️ **未匹配的食材** ({len(report['unmatched_ingredients'])} 个):")
            for ingredient in report['unmatched_ingredients'][:10]:
                print(f"  ❌ {ingredient}")
            if len(report['unmatched_ingredients']) > 10:
                print(f"  ... 还有 {len(report['unmatched_ingredients']) - 10} 个")

        if report['errors']:
            print(f"\n❌ **错误详情** ({len(report['errors'])} 个):")
            for error in report['errors'][:5]:
                print(f"  - {error}")
            if len(report['errors']) > 5:
                print(f"  ... 还有 {len(report['errors']) - 5} 个错误")

        if 'report_file' in report:
            print(f"\n💾 详细报告已保存到: {report['report_file']}")

        print("\n✅ 省厅食谱导入完成！")

def main():
    """主函数"""
    print("🏛️ **省厅食谱智能导入工具**")
    print("="*60)
    print("解析省厅食谱.MD文件，智能导入食谱和食材信息")
    print("="*60)

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 初始化导入器
        importer = ProvincialRecipeImporter()

        # 检查文件是否存在
        if not importer.md_file_path.exists():
            print(f"❌ 文件不存在: {importer.md_file_path}")
            return

        try:
            # 解析MD文件
            recipes = importer.parse_md_file()

            if not recipes:
                print("❌ 没有解析到任何食谱")
                return

            # 显示解析结果
            print(f"\n📊 解析结果:")
            print(f"  📖 总食谱数: {len(recipes)}")

            # 按分类统计
            category_stats = {}
            for recipe in recipes:
                category = recipe['category']
                category_stats[category] = category_stats.get(category, 0) + 1

            print(f"  📂 分类统计:")
            for category, count in category_stats.items():
                print(f"    - {category}: {count} 个")

            # 显示一些食谱示例
            print(f"\n📋 食谱示例 (前5个):")
            for i, recipe in enumerate(recipes[:5], 1):
                ingredient_count = len(recipe['ingredients'])
                print(f"  {i}. {recipe['name']} ({recipe['category']}) - {ingredient_count}个食材")

            # 询问是否继续
            response = input(f"\n是否继续导入这 {len(recipes)} 个食谱？(y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return

            # 执行批量导入
            report = importer.batch_import(recipes, batch_size=5)

            # 显示报告
            importer.print_import_report(report)

            # 显示最终统计
            if report['summary']['recipes_created'] > 0:
                print(f"\n🎉 省厅食谱导入成功！")
                print(f"📊 导入成果:")
                print(f"  ✅ 新增食谱: {report['summary']['recipes_created']} 个")
                print(f"  🔗 食材关联: {report['summary']['ingredient_relations_created']} 个")
                print(f"  📈 成功率: {report['summary']['success_rate']:.1f}%")

                if report['unmatched_ingredients']:
                    print(f"\n💡 建议:")
                    print(f"  - 有 {len(report['unmatched_ingredients'])} 个食材未匹配")
                    print(f"  - 可以手动添加这些食材到系统中")
                    print(f"  - 或者更新食材映射表以提高匹配率")

                print(f"\n🌐 您可以访问 http://127.0.0.1:5000/recipe/ 查看导入的食谱")

        except Exception as e:
            print(f"❌ 导入过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
