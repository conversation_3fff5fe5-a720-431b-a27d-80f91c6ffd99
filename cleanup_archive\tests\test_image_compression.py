#!/usr/bin/env python3
"""
测试图片压缩功能
验证照片是否正确压缩到800*600大小
"""

from PIL import Image
import io
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_image(width, height, color=(255, 0, 0)):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color)
    return img

def test_compression_logic():
    """测试压缩逻辑"""
    print("=== 测试图片压缩逻辑 ===")

    # 测试不同尺寸的图片
    test_cases = [
        (1920, 1080, "横向高清图片"),
        (1080, 1920, "竖向高清图片"),
        (4000, 3000, "超大横向图片"),
        (3000, 4000, "超大竖向图片"),
        (800, 600, "标准尺寸图片"),
        (400, 300, "小尺寸图片"),
        (1600, 1200, "4:3比例图片"),
        (1920, 1200, "16:10比例图片")
    ]

    target_size = (800, 600)
    all_passed = True

    for original_width, original_height, description in test_cases:
        print(f"\n测试: {description} ({original_width}×{original_height})")

        # 创建测试图片
        img = create_test_image(original_width, original_height)

        # 应用压缩逻辑
        compressed_img = compress_image(img, target_size)

        # 验证结果
        if compressed_img.size == target_size:
            print(f"  ✓ 压缩成功: {compressed_img.size}")
        else:
            print(f"  ✗ 压缩失败: 期望{target_size}, 实际{compressed_img.size}")
            all_passed = False

    return all_passed

def compress_image(img, target_size):
    """压缩图片到指定尺寸（复制后端逻辑）"""
    # 转换为RGB模式
    if img.mode in ('RGBA', 'LA', 'P'):
        img = img.convert('RGB')

    # 计算缩放比例，确保图片适合目标尺寸
    img_ratio = img.width / img.height
    target_ratio = target_size[0] / target_size[1]

    if img_ratio > target_ratio:
        # 图片更宽，以宽度为准
        new_width = target_size[0]
        new_height = int(target_size[0] / img_ratio)
    else:
        # 图片更高，以高度为准
        new_height = target_size[1]
        new_width = int(target_size[1] * img_ratio)

    # 调整图片大小
    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

    # 如果需要，创建800*600的白色背景并居中放置图片
    if new_width != target_size[0] or new_height != target_size[1]:
        # 创建白色背景
        background = Image.new('RGB', target_size, (255, 255, 255))

        # 计算居中位置
        x = (target_size[0] - new_width) // 2
        y = (target_size[1] - new_height) // 2

        # 将调整后的图片粘贴到背景上
        background.paste(img, (x, y))
        img = background

    return img

def test_file_size_reduction():
    """测试文件大小压缩效果"""
    print("\n=== 测试文件大小压缩 ===")

    # 创建一个大图片
    large_img = create_test_image(4000, 3000, (128, 128, 255))

    # 保存原始图片到内存
    original_output = io.BytesIO()
    large_img.save(original_output, format='JPEG', quality=95)
    original_size = len(original_output.getvalue())

    # 压缩图片
    compressed_img = compress_image(large_img, (800, 600))

    # 保存压缩图片到内存
    compressed_output = io.BytesIO()
    compressed_img.save(compressed_output, format='JPEG', quality=90, optimize=True)
    compressed_size = len(compressed_output.getvalue())

    # 计算压缩比
    compression_ratio = (1 - compressed_size / original_size) * 100

    print(f"原始图片: 4000×3000, 文件大小: {original_size:,} 字节")
    print(f"压缩图片: 800×600, 文件大小: {compressed_size:,} 字节")
    print(f"压缩比: {compression_ratio:.1f}%")

    if compression_ratio > 80:
        print("✓ 压缩效果良好")
        return True
    elif compression_ratio > 50:
        print("✓ 压缩效果一般")
        return True
    else:
        print("✗ 压缩效果不佳")
        return False

def test_api_code():
    """测试API代码是否正确更新"""
    print("\n=== 测试API代码更新 ===")

    api_file = 'app/routes/daily_management/public_api.py'

    if not os.path.exists(api_file):
        print("✗ API文件不存在")
        return False

    with open(api_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查关键代码
    checks = [
        ('target_size = (800, 600)', '设置目标尺寸为800×600'),
        ('img_ratio = img.width / img.height', '计算图片宽高比'),
        ('target_ratio = target_size[0] / target_size[1]', '计算目标宽高比'),
        ('Image.new(\'RGB\', target_size, (255, 255, 255))', '创建白色背景'),
        ('background.paste(img, (x, y))', '居中粘贴图片'),
        ('quality=90', '设置压缩质量为90%')
    ]

    all_passed = True
    for check_code, description in checks:
        if check_code in content:
            print(f"  ✓ {description}")
        else:
            print(f"  ✗ {description}")
            all_passed = False

    return all_passed

def test_template_update():
    """测试模板更新"""
    print("\n=== 测试模板更新 ===")

    template_file = 'app/templates/daily_management/public_upload_inspection_photo.html'

    if not os.path.exists(template_file):
        print("✗ 模板文件不存在")
        return False

    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 检查压缩提示
    if '照片将自动压缩为800×600像素' in content:
        print("  ✓ 已添加压缩提示信息")
        return True
    else:
        print("  ✗ 未找到压缩提示信息")
        return False

def main():
    """主测试函数"""
    print("图片压缩功能测试")
    print("=" * 50)

    tests = [
        test_compression_logic,
        test_file_size_reduction,
        test_api_code,
        test_template_update
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试失败: {str(e)}")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total}")

    if passed == total:
        print("\n🎉 图片压缩功能配置成功！")
        print("\n压缩规格：")
        print("📏 目标尺寸: 800×600像素")
        print("🎨 输出格式: JPEG")
        print("📊 压缩质量: 90%")
        print("🖼️ 背景填充: 白色")
        print("📐 保持比例: 是")
        print("📍 居中对齐: 是")
        print("\n优势：")
        print("⚡ 上传速度更快")
        print("💾 存储空间更小")
        print("📱 移动端友好")
        print("🔄 处理速度更快")
    else:
        print("\n❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
