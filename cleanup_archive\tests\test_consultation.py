#!/usr/bin/env python3
"""
测试在线咨询功能
"""

import os
import sys
import requests
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_consultation_api():
    """测试在线咨询API"""
    base_url = "http://127.0.0.1:5000"
    
    print("测试在线咨询API...")
    print("-" * 50)
    
    # 测试数据
    test_data = {
        "name": "测试用户",
        "contact_type": "微信",
        "contact_value": "test_user_123",
        "content": "这是一个测试咨询，想了解系统的功能和价格。"
    }
    
    try:
        # 发送POST请求
        response = requests.post(
            f"{base_url}/api/consultation/submit",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"请求状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 咨询提交成功！")
                print(f"  咨询ID: {result.get('consultation_id')}")
                print(f"  消息: {result.get('message')}")
                return True
            else:
                print(f"✗ 咨询提交失败: {result.get('message')}")
                return False
        else:
            print(f"✗ HTTP请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ 网络请求异常: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ 其他异常: {str(e)}")
        return False

def test_form_validation():
    """测试表单验证"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n测试表单验证...")
    print("-" * 50)
    
    # 测试空数据
    test_cases = [
        {
            "name": "测试空姓名",
            "data": {
                "name": "",
                "contact_type": "微信",
                "contact_value": "test123",
                "content": "测试内容"
            },
            "expected_error": "请输入您的姓名"
        },
        {
            "name": "测试空联系方式",
            "data": {
                "name": "测试用户",
                "contact_type": "微信",
                "contact_value": "",
                "content": "测试内容"
            },
            "expected_error": "请输入您的联系方式"
        },
        {
            "name": "测试空咨询内容",
            "data": {
                "name": "测试用户",
                "contact_type": "微信",
                "contact_value": "test123",
                "content": ""
            },
            "expected_error": "请输入咨询内容"
        },
        {
            "name": "测试错误手机号格式",
            "data": {
                "name": "测试用户",
                "contact_type": "电话",
                "contact_value": "123456",
                "content": "测试内容测试内容测试内容"
            },
            "expected_error": "请输入正确的手机号码格式"
        },
        {
            "name": "测试错误邮箱格式",
            "data": {
                "name": "测试用户",
                "contact_type": "邮箱",
                "contact_value": "invalid-email",
                "content": "测试内容测试内容测试内容"
            },
            "expected_error": "请输入正确的邮箱格式"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{test_case['name']}:")
        
        try:
            response = requests.post(
                f"{base_url}/api/consultation/submit",
                json=test_case['data'],
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('success'):
                    error_message = result.get('message', '')
                    if test_case['expected_error'] in error_message:
                        print(f"  ✓ 验证通过: {error_message}")
                    else:
                        print(f"  ✗ 验证失败: 期望包含 '{test_case['expected_error']}', 实际 '{error_message}'")
                else:
                    print(f"  ✗ 验证失败: 期望失败但成功了")
            else:
                print(f"  ✗ HTTP请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ 请求异常: {str(e)}")

def test_database_connection():
    """测试数据库连接和模型"""
    print("\n测试数据库连接...")
    print("-" * 50)
    
    try:
        from app import create_app, db
        from app.models_consultation import OnlineConsultation
        
        app = create_app()
        with app.app_context():
            # 测试查询
            consultations = OnlineConsultation.query.limit(5).all()
            print(f"✓ 数据库连接成功，找到 {len(consultations)} 条咨询记录")
            
            # 显示最近的咨询
            if consultations:
                print("\n最近的咨询记录:")
                for consultation in consultations:
                    print(f"  ID: {consultation.id}, 姓名: {consultation.name}, 状态: {consultation.status}")
            
            # 测试统计功能
            stats = OnlineConsultation.get_statistics()
            print(f"\n统计信息:")
            print(f"  状态统计: {stats['status_stats']}")
            print(f"  联系方式统计: {stats['contact_type_stats']}")
            
            return True
            
    except Exception as e:
        print(f"✗ 数据库测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("智慧食堂管理系统 - 在线咨询功能测试")
    print("=" * 60)
    
    # 测试数据库连接
    db_ok = test_database_connection()
    
    if not db_ok:
        print("\n✗ 数据库测试失败，请先运行 init_consultation_db.py 初始化数据库")
        return
    
    # 测试API功能
    api_ok = test_consultation_api()
    
    # 测试表单验证
    test_form_validation()
    
    print("\n" + "=" * 60)
    if api_ok:
        print("✓ 在线咨询功能测试完成！")
        print("\n建议测试步骤：")
        print("1. 访问 http://127.0.0.1:5000 查看首页咨询表单")
        print("2. 填写并提交咨询表单")
        print("3. 以管理员身份登录系统")
        print("4. 访问 /consultation/list 查看咨询列表")
        print("5. 点击查看详情，测试回复功能")
    else:
        print("✗ 在线咨询功能测试失败！")
        print("\n请检查：")
        print("1. 应用是否正在运行 (python app.py)")
        print("2. 数据库是否已初始化")
        print("3. 网络连接是否正常")
    print("=" * 60)

if __name__ == '__main__':
    main()
