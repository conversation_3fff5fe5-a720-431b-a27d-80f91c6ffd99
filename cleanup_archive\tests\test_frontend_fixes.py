#!/usr/bin/env python3
"""
前端修复验证脚本
验证所有前端错误修复是否生效
"""

import os
import sys
import requests
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_frontend_fixes():
    """测试前端修复"""
    print("🧪 开始测试前端修复...")
    print("=" * 60)
    
    # 测试服务器是否运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        print("🌐 启动浏览器测试...")
        
        # 测试主页加载
        print("\n📄 测试主页加载...")
        driver.get('http://localhost:5000')
        
        # 等待页面加载完成
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        print("✅ 主页加载成功")
        
        # 检查页面标题
        title = driver.title
        if "智慧食堂" in title:
            print(f"✅ 页面标题正确: {title}")
        else:
            print(f"⚠️ 页面标题异常: {title}")
        
        # 检查jQuery是否加载
        jquery_loaded = driver.execute_script("return typeof $ !== 'undefined';")
        if jquery_loaded:
            print("✅ jQuery 加载成功")
        else:
            print("❌ jQuery 未加载")
        
        # 检查Bootstrap是否加载
        bootstrap_loaded = driver.execute_script("return typeof bootstrap !== 'undefined';")
        if bootstrap_loaded:
            print("✅ Bootstrap 加载成功")
        else:
            print("❌ Bootstrap 未加载")
        
        # 检查Chart.js是否加载
        chartjs_loaded = driver.execute_script("return typeof Chart !== 'undefined';")
        if chartjs_loaded:
            print("✅ Chart.js 加载成功")
        else:
            print("❌ Chart.js 未加载")
        
        # 检查错误监控器是否加载
        error_monitor_loaded = driver.execute_script("return typeof ErrorMonitor !== 'undefined';")
        if error_monitor_loaded:
            print("✅ 错误监控器加载成功")
        else:
            print("❌ 错误监控器未加载")
        
        # 检查控制台错误
        print("\n🔍 检查控制台错误...")
        logs = driver.get_log('browser')
        error_count = 0
        warning_count = 0
        
        for log in logs:
            if log['level'] == 'SEVERE':
                error_count += 1
                print(f"❌ 错误: {log['message']}")
            elif log['level'] == 'WARNING':
                warning_count += 1
                print(f"⚠️ 警告: {log['message']}")
        
        if error_count == 0:
            print("✅ 无严重错误")
        else:
            print(f"❌ 发现 {error_count} 个严重错误")
        
        if warning_count == 0:
            print("✅ 无警告")
        else:
            print(f"⚠️ 发现 {warning_count} 个警告")
        
        # 测试在线咨询表单
        print("\n📝 测试在线咨询表单...")
        try:
            # 查找表单元素
            name_input = driver.find_element(By.ID, "name")
            contact_type_select = driver.find_element(By.ID, "contact_type")
            contact_value_input = driver.find_element(By.ID, "contact_value")
            content_textarea = driver.find_element(By.ID, "content")
            submit_btn = driver.find_element(By.ID, "submitBtn")
            
            if all([name_input, contact_type_select, contact_value_input, content_textarea, submit_btn]):
                print("✅ 在线咨询表单元素完整")
                
                # 测试表单交互
                name_input.send_keys("测试用户")
                contact_value_input.send_keys("<EMAIL>")
                content_textarea.send_keys("这是一个前端测试消息")
                
                print("✅ 表单输入测试成功")
            else:
                print("❌ 在线咨询表单元素不完整")
                
        except Exception as e:
            print(f"❌ 在线咨询表单测试失败: {e}")
        
        # 测试轮播图
        print("\n🎠 测试轮播图...")
        try:
            carousel = driver.find_element(By.ID, "heroCarousel")
            if carousel:
                print("✅ 轮播图容器存在")
                
                # 检查轮播图是否初始化
                carousel_initialized = driver.execute_script("""
                    const carousel = document.getElementById('heroCarousel');
                    return carousel && carousel.classList.contains('carousel');
                """)
                
                if carousel_initialized:
                    print("✅ 轮播图初始化成功")
                else:
                    print("⚠️ 轮播图可能未正确初始化")
            else:
                print("❌ 轮播图容器不存在")
                
        except Exception as e:
            print(f"❌ 轮播图测试失败: {e}")
        
        # 测试图表
        print("\n📊 测试图表...")
        try:
            charts = ['efficiencyChart', 'safetyChart', 'satisfactionChart']
            for chart_id in charts:
                chart_element = driver.find_element(By.ID, chart_id)
                if chart_element:
                    print(f"✅ {chart_id} 元素存在")
                else:
                    print(f"❌ {chart_id} 元素不存在")
                    
        except Exception as e:
            print(f"❌ 图表测试失败: {e}")
        
        # 生成测试报告
        print("\n📋 生成测试报告...")
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'page_title': driver.title,
            'jquery_loaded': jquery_loaded,
            'bootstrap_loaded': bootstrap_loaded,
            'chartjs_loaded': chartjs_loaded,
            'error_monitor_loaded': error_monitor_loaded,
            'console_errors': error_count,
            'console_warnings': warning_count,
            'url': driver.current_url
        }
        
        print("📊 测试结果总结:")
        print(f"   页面标题: {report['page_title']}")
        print(f"   jQuery: {'✅' if report['jquery_loaded'] else '❌'}")
        print(f"   Bootstrap: {'✅' if report['bootstrap_loaded'] else '❌'}")
        print(f"   Chart.js: {'✅' if report['chartjs_loaded'] else '❌'}")
        print(f"   错误监控: {'✅' if report['error_monitor_loaded'] else '❌'}")
        print(f"   控制台错误: {report['console_errors']}")
        print(f"   控制台警告: {report['console_warnings']}")
        
        # 保存测试报告
        import json
        with open('frontend_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 测试报告已保存到 frontend_test_report.json")
        
        return error_count == 0 and all([
            jquery_loaded, bootstrap_loaded, chartjs_loaded, error_monitor_loaded
        ])
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        return False
        
    finally:
        if 'driver' in locals():
            driver.quit()

def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 测试API端点...")
    
    endpoints = [
        '/api/carousel/list',
        '/consultation/api/submit'  # 这个需要POST数据
    ]
    
    for endpoint in endpoints:
        try:
            if endpoint == '/consultation/api/submit':
                # 测试POST请求
                data = {
                    'name': '测试用户',
                    'contact_type': '邮箱',
                    'contact_value': '<EMAIL>',
                    'content': '这是一个API测试消息'
                }
                response = requests.post(f'http://localhost:5000{endpoint}', 
                                       json=data, timeout=5)
            else:
                response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
            else:
                print(f"⚠️ {endpoint} - 状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - 请求失败: {e}")

if __name__ == '__main__':
    print("🚀 前端修复验证测试")
    print("=" * 60)
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试前端
    success = test_frontend_fixes()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有前端修复验证通过！")
        print("✅ 系统可以正常使用")
    else:
        print("⚠️ 部分测试未通过，请检查具体错误信息")
        print("📝 建议查看 frontend_test_report.json 了解详细信息")
    
    print("\n💡 调试提示:")
    print("   - 在浏览器控制台输入 debugFrontend() 查看前端状态")
    print("   - 在浏览器控制台输入 ErrorMonitor.getErrorReport() 查看错误报告")
    print("   - 检查网络连接和资源加载情况")
