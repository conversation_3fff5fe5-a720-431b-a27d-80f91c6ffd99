#!/usr/bin/env python3
"""
测试修复后的在线咨询功能
验证是否按照README.md最佳实践正常工作
"""

import os
import sys
import requests
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_submission():
    """测试API提交功能"""
    print("🧪 测试API提交功能...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试数据
    test_data = {
        "name": "最佳实践测试",
        "contact_type": "微信",
        "contact_value": "bestpractice123",
        "content": "这是按照README.md最佳实践修复后的测试，验证时间精度问题是否解决。"
    }
    
    try:
        # 发送POST请求
        response = requests.post(
            f"{base_url}/api/consultation/submit",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('success'):
                print("✅ API提交测试成功！")
                print(f"咨询ID: {result.get('consultation_id')}")
                return True
            else:
                print(f"❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 应用可能未运行")
        print("请先启动应用: python app.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_database_direct():
    """直接测试数据库操作"""
    print("\n🗄️ 测试数据库直接操作...")
    print("-" * 50)
    
    try:
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # 测试插入（按照最佳实践）
            print("📝 测试直接插入...")
            insert_sql = text("""
                INSERT INTO online_consultations 
                (name, contact_type, contact_value, content)
                OUTPUT inserted.id, inserted.created_at, inserted.updated_at
                VALUES 
                ('数据库测试', '电话', '13800138000', '直接数据库测试，验证时间字段')
            """)
            
            result = db.session.execute(insert_sql)
            record = result.fetchone()
            db.session.commit()
            
            print(f"✅ 插入成功")
            print(f"  ID: {record[0]}")
            print(f"  创建时间: {record[1]}")
            print(f"  更新时间: {record[2]}")
            
            # 测试更新
            print("\n🔄 测试更新操作...")
            update_sql = text("""
                UPDATE online_consultations 
                SET content = '更新后的内容 - 验证触发器'
                WHERE id = :id
            """)
            
            db.session.execute(update_sql, {'id': record[0]})
            db.session.commit()
            
            # 检查更新后的时间
            check_sql = text("""
                SELECT created_at, updated_at, content
                FROM online_consultations 
                WHERE id = :id
            """)
            
            result = db.session.execute(check_sql, {'id': record[0]})
            updated_record = result.fetchone()
            
            print(f"✅ 更新成功")
            print(f"  创建时间: {updated_record[0]} (保持不变)")
            print(f"  更新时间: {updated_record[1]} (自动更新)")
            print(f"  内容: {updated_record[2]}")
            
            # 清理测试数据
            db.session.execute(text("DELETE FROM online_consultations WHERE id = :id"), {'id': record[0]})
            db.session.commit()
            print("✅ 测试数据已清理")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_table_structure():
    """测试表结构"""
    print("\n📊 验证表结构...")
    print("-" * 50)
    
    try:
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # 检查表结构
            result = db.session.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, DATETIME_PRECISION, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'online_consultations' 
                AND DATA_TYPE IN ('datetime2', 'nvarchar')
                ORDER BY ORDINAL_POSITION
            """))
            
            print("关键字段结构：")
            print("-" * 70)
            print(f"{'字段名':<20} {'类型':<15} {'精度':<8} {'默认值':<20}")
            print("-" * 70)
            
            datetime_fields = []
            for row in result:
                column_name = row[0]
                data_type = row[1]
                precision = row[2] if row[2] is not None else ''
                default_value = row[3] if row[3] else ''
                
                print(f"{column_name:<20} {data_type:<15} {str(precision):<8} {str(default_value):<20}")
                
                if data_type == 'datetime2':
                    datetime_fields.append((column_name, precision))
            
            print("-" * 70)
            
            # 验证DATETIME2精度
            print("\n🎯 DATETIME2字段验证：")
            for field_name, precision in datetime_fields:
                if precision == 1:
                    print(f"✅ {field_name}: DATETIME2({precision}) - 符合最佳实践")
                else:
                    print(f"⚠️  {field_name}: DATETIME2({precision}) - 可能需要调整")
            
            return True
            
    except Exception as e:
        print(f"❌ 表结构检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 在线咨询功能最终测试")
    print("=" * 60)
    print("验证按照README.md最佳实践修复后的功能")
    print("=" * 60)
    
    # 测试表结构
    structure_ok = test_table_structure()
    
    # 测试数据库操作
    db_ok = test_database_direct()
    
    # 测试API
    print("\n" + "=" * 60)
    print("是否要测试API功能？(需要应用正在运行)")
    choice = input("输入 y 继续，其他键跳过: ").lower().strip()
    
    api_ok = True
    if choice == 'y':
        api_ok = test_api_submission()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"表结构验证: {'✅ 通过' if structure_ok else '❌ 失败'}")
    print(f"数据库操作: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"API功能测试: {'✅ 通过' if api_ok else '❌ 失败' if choice == 'y' else '⏭️ 跳过'}")
    
    if structure_ok and db_ok and api_ok:
        print("\n🎉 所有测试通过！")
        print("在线咨询功能已按照README.md最佳实践成功修复")
        print("\n📋 现在可以：")
        print("1. 正常使用首页咨询表单")
        print("2. 管理员查看咨询列表")
        print("3. 不再有时间精度错误")
    else:
        print("\n⚠️  部分测试失败，请检查相关问题")

if __name__ == '__main__':
    main()
