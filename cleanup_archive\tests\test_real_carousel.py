#!/usr/bin/env python3
"""
测试真正的轮播图效果
验证Bootstrap轮播功能是否正常工作
"""

import requests
from bs4 import BeautifulSoup
import time

def test_carousel_html_structure():
    """测试轮播图HTML结构"""
    print("🏗️  测试轮播图HTML结构...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # 检查轮播图容器
            carousel = soup.find('div', {'id': 'heroCarousel'})
            if carousel:
                print("✅ 轮播图容器存在")
                
                # 检查Bootstrap类
                classes = carousel.get('class', [])
                if 'carousel' in classes and 'slide' in classes:
                    print("✅ Bootstrap轮播类正确")
                else:
                    print(f"❌ Bootstrap轮播类错误: {classes}")
                    return False
                
                # 检查data属性
                if carousel.get('data-bs-ride') == 'carousel':
                    print("✅ 自动轮播属性正确")
                else:
                    print("❌ 自动轮播属性错误")
                    return False
            else:
                print("❌ 轮播图容器不存在")
                return False
            
            # 检查指示器
            indicators = soup.find('div', {'id': 'heroCarouselIndicators'})
            if indicators and 'carousel-indicators' in indicators.get('class', []):
                print("✅ 轮播指示器容器正确")
            else:
                print("❌ 轮播指示器容器错误")
                return False
            
            # 检查内容容器
            inner = soup.find('div', {'id': 'heroCarouselInner'})
            if inner and 'carousel-inner' in inner.get('class', []):
                print("✅ 轮播内容容器正确")
            else:
                print("❌ 轮播内容容器错误")
                return False
            
            # 检查控制按钮
            prev_btn = soup.find('button', class_='carousel-control-prev')
            next_btn = soup.find('button', class_='carousel-control-next')
            
            if prev_btn and next_btn:
                print("✅ 轮播控制按钮存在")
                
                # 检查按钮属性
                if (prev_btn.get('data-bs-target') == '#heroCarousel' and 
                    next_btn.get('data-bs-target') == '#heroCarousel'):
                    print("✅ 控制按钮目标正确")
                else:
                    print("❌ 控制按钮目标错误")
                    return False
            else:
                print("❌ 轮播控制按钮缺失")
                return False
            
            return True
            
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_carousel_javascript():
    """测试轮播图JavaScript"""
    print("\n⚙️  测试轮播图JavaScript...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查JavaScript函数
            js_checks = [
                ("initHeroCarousel", "轮播图初始化函数"),
                ("createCarouselSlides", "创建轮播项函数"),
                ("showEmptyState", "空状态显示函数"),
                ("bootstrap.Carousel", "Bootstrap轮播初始化"),
                ("interval: 4000", "4秒自动切换"),
                ("wrap: true", "循环轮播"),
                ("touch: true", "触摸支持"),
                ("pause: 'hover'", "悬停暂停"),
            ]
            
            results = []
            for js_check, description in js_checks:
                if js_check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查是否移除了复杂的类
            if 'class HeroCarousel' not in content:
                print("  ✅ 复杂的类已移除")
                results.append(True)
            else:
                print("  ❌ 仍有复杂的类")
                results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 JavaScript检查: {success_count}/{total_count} 通过")
            return success_count >= total_count * 0.8
            
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_carousel_css():
    """测试轮播图CSS样式"""
    print("\n🎨 测试轮播图CSS样式...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查CSS样式
            css_checks = [
                ("#heroCarousel", "轮播图容器样式"),
                (".carousel-item", "轮播项样式"),
                ("height: 400px", "轮播图高度"),
                ("object-fit: cover", "图片适配"),
                ("transition: transform 0.6s", "过渡动画"),
                (".carousel-indicators", "指示器样式"),
                (".carousel-control-prev", "控制按钮样式"),
                ("border-radius: 50%", "圆形按钮"),
                ("background: rgba(0, 0, 0, 0.4)", "半透明背景"),
            ]
            
            results = []
            for css_check, description in css_checks:
                if css_check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查响应式设计
            if "@media (max-width: 768px)" in content:
                print("  ✅ 响应式设计存在")
                results.append(True)
            else:
                print("  ❌ 响应式设计缺失")
                results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 CSS样式检查: {success_count}/{total_count} 通过")
            return success_count >= total_count * 0.8
            
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_api_data():
    """测试API数据"""
    print("\n📡 测试轮播图API数据...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        response = requests.get(f"{base_url}/api/carousel/list", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                carousels = data.get('data', [])
                count = len(carousels)
                
                print(f"✅ API响应成功，获取到 {count} 个轮播图")
                
                if count > 0:
                    print("📋 轮播图数据:")
                    for i, carousel in enumerate(carousels[:3], 1):
                        title = carousel.get('title', '无标题')
                        image = carousel.get('image_path', '无图片')
                        link = carousel.get('link_url', '/')
                        print(f"  {i}. {title}")
                        print(f"     图片: {image}")
                        print(f"     链接: {link}")
                    
                    print("\n🎠 预期轮播效果:")
                    print("1. 页面加载后自动开始轮播")
                    print("2. 每4秒自动切换到下一张")
                    print("3. 鼠标悬停时暂停轮播")
                    print("4. 点击指示器可跳转到指定图片")
                    print("5. 点击左右箭头可手动切换")
                    print("6. 支持触摸滑动（移动端）")
                    
                    return True
                else:
                    print("⚠️  没有轮播图数据，将显示空状态")
                    print("💡 建议: 在后台添加一些轮播图数据")
                    return True
            else:
                print(f"❌ API返回错误: {data.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🎠 真正的轮播图效果测试")
    print("=" * 60)
    print("验证Bootstrap轮播功能是否正常工作")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("HTML结构", test_carousel_html_structure()),
        ("JavaScript功能", test_carousel_javascript()),
        ("CSS样式", test_carousel_css()),
        ("API数据", test_api_data()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 真正的轮播图效果已实现！")
        print("\n✨ 轮播功能特性:")
        print("1. ✅ 使用Bootstrap原生轮播组件")
        print("2. ✅ 4秒自动切换间隔")
        print("3. ✅ 鼠标悬停暂停轮播")
        print("4. ✅ 循环播放（最后一张回到第一张）")
        print("5. ✅ 触摸滑动支持（移动端）")
        print("6. ✅ 平滑的过渡动画效果")
        print("7. ✅ 点击指示器跳转")
        print("8. ✅ 左右箭头手动控制")
        
        print("\n🎯 轮播效果说明:")
        print("• 页面加载后轮播图自动开始播放")
        print("• 每张图片显示4秒后自动切换")
        print("• 鼠标悬停在轮播图上时暂停播放")
        print("• 鼠标离开后继续自动播放")
        print("• 播放到最后一张后自动回到第一张")
        print("• 所有切换都有平滑的过渡动画")
        
        print("\n📱 交互功能:")
        print("• 点击底部圆点指示器跳转到对应图片")
        print("• 点击左右箭头按钮手动切换")
        print("• 移动端支持左右滑动切换")
        print("• 点击图片可跳转到设置的链接")
        
        print("\n💡 使用建议:")
        print("1. 在后台上传3-5张轮播图获得最佳效果")
        print("2. 图片建议尺寸: 1200x600像素")
        print("3. 设置有意义的标题和跳转链接")
        print("4. 定期更新轮播图内容保持新鲜感")
        
    else:
        print("\n⚠️  部分测试失败")
        print("\n🔧 建议操作:")
        
        if not tests[0][1]:  # HTML结构
            print("1. 检查HTML模板结构")
            print("2. 确认Bootstrap类名正确")
        
        if not tests[1][1]:  # JavaScript
            print("3. 检查JavaScript代码")
            print("4. 确认Bootstrap库已加载")
        
        if not tests[2][1]:  # CSS样式
            print("5. 检查CSS样式定义")
            print("6. 确认样式选择器正确")
        
        if not tests[3][1]:  # API数据
            print("7. 检查轮播图API")
            print("8. 在后台添加轮播图数据")
        
        print("\n9. 清除浏览器缓存")
        print("10. 重启应用服务器")
        print("11. 再次运行此测试")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
