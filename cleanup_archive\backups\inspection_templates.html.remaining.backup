{% extends 'base.html' %}

{% block title %}检查模板管理{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .template-card {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
        overflow: hidden;
        height: 100%;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .template-card .card-header {
        background: linear-gradient(135deg, #f8f9fc, #ffffff);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1rem 1.25rem;
    }

    .template-card .card-body {
        padding: 1.25rem;
    }

    .template-card .badge-default {
        background-color: #4e73df;
        color: white;
    }

    .template-items {
        max-height: 200px;
        overflow-y: auto;
        margin-top: 1rem;
    }

    .template-item {
        padding: 0.5rem;
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
        background-color: #f8f9fc;
        border-left: 3px solid #4e73df;
    }

    .template-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 1rem;
    }

    .template-actions .btn {
        margin-left: 0.5rem;
    }

    .category-filter {
        margin-bottom: 1.5rem;
    }

    .category-filter .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .category-filter .btn.active {
        background-color: #4e73df;
        color: white;
    }

    /* 模态框样式 */
    .modal-header {
        background: linear-gradient(135deg, #4e73df, #224abe);
        color: white;
        border-radius: 0.3rem 0.3rem 0 0;
    }

    .modal-footer {
        background-color: #f8f9fc;
    }

    .item-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .item-row {
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
        background-color: #f8f9fc;
        display: flex;
        align-items: center;
    }

    .item-row:hover {
        background-color: #eaecf4;
    }

    .item-row .drag-handle {
        cursor: move;
        margin-right: 0.5rem;
        color: #b7b9cc;
    }

    .item-row .form-group {
        margin-bottom: 0;
        flex-grow: 1;
    }

    .item-row .btn-remove {
        color: #e74a3b;
        background: none;
        border: none;
        font-size: 1.2rem;
        padding: 0;
        margin-left: 0.5rem;
    }

    .add-item-btn {
        width: 100%;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">检查模板管理</h1>

    <!-- 操作按钮 -->
    <div class="mb-4">
        <button class="btn btn-primary" data-toggle="modal" data-target="#createTemplateModal">
            <i class="fas fa-plus mr-1"></i> 创建新模板
        </button>
    </div>

    <!-- 分类筛选 -->
    <div class="category-filter">
        <button class="btn btn-outline-primary active" data-category="all">全部</button>
        <button class="btn btn-outline-primary" data-category="卫生检查">卫生检查</button>
        <button class="btn btn-outline-primary" data-category="食品安全">食品安全</button>
        <button class="btn btn-outline-primary" data-category="设备设施">设备设施</button>
        <button class="btn btn-outline-primary" data-category="人员管理">人员管理</button>
    </div>

    <!-- 模板列表 -->
    <div class="row" id="templateList">
        <!-- 模板卡片将通过JavaScript动态加载 -->
    </div>
</div>

<!-- 创建模板模态框 -->
<div class="modal fade" id="createTemplateModal" tabindex="-1" role="dialog" aria-labelledby="createTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createTemplateModalLabel">创建检查模板</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <div class="form-group">
                        <label for="templateName">模板名称</label>
                        <input type="text" class="form-control" id="templateName" required>
                    </div>
                    <div class="form-group">
                        <label for="templateDescription">模板描述</label>
                        <textarea class="form-control" id="templateDescription" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="templateCategory">模板分类</label>
                        <select class="form-control" id="templateCategory" required>
                            <option value="卫生检查">卫生检查</option>
                            <option value="食品安全">食品安全</option>
                            <option value="设备设施">设备设施</option>
                            <option value="人员管理">人员管理</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>检查项目</label>
                        <div class="item-list" id="itemList">
                            <!-- 检查项目将通过JavaScript动态添加 -->
                        </div>
                        <button type="button" class="btn btn-outline-primary add-item-btn" id="addItemBtn">
                            <i class="fas fa-plus mr-1"></i> 添加检查项目
                        </button>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="isDefault">
                            <label class="custom-control-label" for="isDefault">设为默认模板</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTemplateBtn">保存模板</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑模板模态框 -->
<div class="modal fade" id="editTemplateModal" tabindex="-1" role="dialog" aria-labelledby="editTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTemplateModalLabel">编辑检查模板</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editTemplateForm">
                    <input type="hidden" id="editTemplateId">
                    <div class="form-group">
                        <label for="editTemplateName">模板名称</label>
                        <input type="text" class="form-control" id="editTemplateName" required>
                    </div>
                    <div class="form-group">
                        <label for="editTemplateDescription">模板描述</label>
                        <textarea class="form-control" id="editTemplateDescription" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editTemplateCategory">模板分类</label>
                        <select class="form-control" id="editTemplateCategory" required>
                            <option value="卫生检查">卫生检查</option>
                            <option value="食品安全">食品安全</option>
                            <option value="设备设施">设备设施</option>
                            <option value="人员管理">人员管理</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>检查项目</label>
                        <div class="item-list" id="editItemList">
                            <!-- 检查项目将通过JavaScript动态添加 -->
                        </div>
                        <button type="button" class="btn btn-outline-primary add-item-btn" id="editAddItemBtn">
                            <i class="fas fa-plus mr-1"></i> 添加检查项目
                        </button>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="editIsDefault">
                            <label class="custom-control-label" for="editIsDefault">设为默认模板</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="updateTemplateBtn">更新模板</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="https://cdn.jsdelivr.net/npm/sortablejs@1.10.2/Sortable.min.js"></script>
<script nonce="{{ csp_nonce }}">
    // 模板数据将通过API获取
    let templates = [];

    // 页面加载完成后执行
    $(document).ready(function() {
        // 获取模板数据
        fetchTemplates();

        // 初始化分类筛选
        initCategoryFilter();

        // 初始化创建模板表单
        initCreateTemplateForm();

        // 初始化编辑模板表单
        initEditTemplateForm();
    });

    // 获取模板数据
    function fetchTemplates() {
        // 显示加载中
        $('#templateList').html('<div class="col-12 text-center py-5"><i class="fas fa-spinner fa-spin fa-3x"></i></div>');

        // 发送API请求获取模板数据
        $.ajax({
            url: '/api/v2/inspection-templates',
            method: 'GET',
            success: function(response) {
                templates = response;
                renderTemplates(templates);
            },
            error: function(error) {
                console.error('获取模板数据失败:', error);
                $('#templateList').html('<div class="col-12 text-center py-5"><div class="alert alert-danger">获取模板数据失败</div></div>');
            }
        });
    }

    // 渲染模板列表
    function renderTemplates(templates, category = 'all') {
        // 清空模板列表
        $('#templateList').empty();

        // 筛选模板
        let filteredTemplates = templates;
        if (category !== 'all') {
            filteredTemplates = templates.filter(template => template.category === category);
        }

        // 如果没有模板，显示提示信息
        if (filteredTemplates.length === 0) {
            $('#templateList').html('<div class="col-12 text-center py-5"><div class="alert alert-info">暂无模板数据</div></div>');
            return;
        }

        // 渲染模板卡片
        filteredTemplates.forEach(template => {
            const card = `
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card template-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">${template.name}</h6>
                            ${template.is_default ? '<span class="badge badge-default">默认</span>' : ''}
                        </div>
                        <div class="card-body">
                            <p class="text-muted small">${template.description || '无描述'}</p>
                            <div class="badge badge-light mb-2">${template.category}</div>
                            <div class="template-items">
                                ${renderTemplateItems(template.items)}
                            </div>
                            <div class="template-actions">
                                <button class="btn btn-sm btn-info" onclick="useTemplate(${template.id})">
                                    <i class="fas fa-check mr-1"></i> 使用
                                </button>
                                <button class="btn btn-sm btn-primary" onclick="editTemplate(${template.id})">
                                    <i class="fas fa-edit mr-1"></i> 编辑
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteTemplate(${template.id})">
                                    <i class="fas fa-trash mr-1"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#templateList').append(card);
        });
    }

    // 渲染模板项目
    function renderTemplateItems(items) {
        if (!items || items.length === 0) {
            return '<div class="text-muted">暂无检查项目</div>';
        }

        let html = '';
        items.forEach(item => {
            html += `
                <div class="template-item">
                    <div class="font-weight-bold">${item.name}</div>
                    <div class="text-muted small">${item.description || '无描述'}</div>
                </div>
            `;
        });

        return html;
    }

    // 初始化分类筛选
    function initCategoryFilter() {
        $('.category-filter .btn').click(function() {
            // 移除所有按钮的active类
            $('.category-filter .btn').removeClass('active');
            // 添加当前按钮的active类
            $(this).addClass('active');

            // 获取分类
            const category = $(this).data('category');

            // 重新渲染模板列表
            renderTemplates(templates, category);
        });
    }

    // 初始化创建模板表单
    function initCreateTemplateForm() {
        // 添加检查项目按钮点击事件
        $('#addItemBtn').click(function() {
            addItemRow();
        });

        // 保存模板按钮点击事件
        $('#saveTemplateBtn').click(function() {
            saveTemplate();
        });

        // 初始化拖拽排序
        new Sortable(document.getElementById('itemList'), {
            handle: '.drag-handle',
            animation: 150
        });

        // 初始化时添加一个空的检查项目
        addItemRow();
    }

    // 添加检查项目行
    function addItemRow(name = '', description = '', required = true) {
        const itemRow = `
            <div class="item-row">
                <div class="drag-handle">
                    <i class="fas fa-grip-vertical"></i>
                </div>
                <div class="form-group mr-2">
                    <input type="text" class="form-control form-control-sm item-name" placeholder="检查项目名称" value="${name}" required>
                </div>
                <div class="form-group mr-2">
                    <input type="text" class="form-control form-control-sm item-description" placeholder="检查项目描述" value="${description}">
                </div>
                <div class="custom-control custom-checkbox mr-2">
                    <input type="checkbox" class="custom-control-input item-required" id="required_${Date.now()}" ${required ? 'checked' : ''}>
                    <label class="custom-control-label" for="required_${Date.now()}">必填</label>
                </div>
                <button type="button" class="btn-remove" onclick="removeItemRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        $('#itemList').append(itemRow);
    }

    // 移除检查项目行
    function removeItemRow(button) {
        $(button).closest('.item-row').remove();
    }

    // 保存模板
    function saveTemplate() {
        // 获取表单数据
        const name = $('#templateName').val();
        const description = $('#templateDescription').val();
        const category = $('#templateCategory').val();
        const isDefault = $('#isDefault').prop('checked');

        // 获取检查项目
        const items = [];
        $('.item-row').each(function() {
            const name = $(this).find('.item-name').val();
            const description = $(this).find('.item-description').val();
            const required = $(this).find('.item-required').prop('checked');

            if (name) {
                items.push({
                    name: name,
                    description: description,
                    required: required
                });
            }
        });

        // 验证表单
        if (!name) {
            alert('请输入模板名称');
            return;
        }

        if (!category) {
            alert('请选择模板分类');
            return;
        }

        if (items.length === 0) {
            alert('请添加至少一个检查项目');
            return;
        }

        // 构建请求数据
        const data = {
            name: name,
            description: description,
            category: category,
            items: items,
            is_default: isDefault
        };

        // 发送API请求创建模板
        $.ajax({
            url: '/api/v2/inspection-templates',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(response) {
                // 关闭模态框
                $('#createTemplateModal').modal('hide');

                // 重置表单
                resetCreateTemplateForm();

                // 重新获取模板数据
                fetchTemplates();

                // 显示成功提示
                alert('模板创建成功');
            },
            error: function(error) {
                console.error('创建模板失败:', error);
                alert('创建模板失败: ' + (error.responseJSON?.error || '未知错误'));
            }
        });
    }

    // 重置创建模板表单
    function resetCreateTemplateForm() {
        $('#templateName').val('');
        $('#templateDescription').val('');
        $('#templateCategory').val('卫生检查');
        $('#isDefault').prop('checked', false);

        // 清空检查项目列表
        $('#itemList').empty();

        // 添加一个空的检查项目
        addItemRow();
    }

    // 初始化编辑模板表单
    function initEditTemplateForm() {
        // 添加检查项目按钮点击事件
        $('#editAddItemBtn').click(function() {
            addEditItemRow();
        });

        // 更新模板按钮点击事件
        $('#updateTemplateBtn').click(function() {
            updateTemplate();
        });

        // 初始化拖拽排序
        new Sortable(document.getElementById('editItemList'), {
            handle: '.drag-handle',
            animation: 150
        });
    }

    // 添加编辑检查项目行
    function addEditItemRow(name = '', description = '', required = true) {
        const itemRow = `
            <div class="item-row">
                <div class="drag-handle">
                    <i class="fas fa-grip-vertical"></i>
                </div>
                <div class="form-group mr-2">
                    <input type="text" class="form-control form-control-sm item-name" placeholder="检查项目名称" value="${name}" required>
                </div>
                <div class="form-group mr-2">
                    <input type="text" class="form-control form-control-sm item-description" placeholder="检查项目描述" value="${description}">
                </div>
                <div class="custom-control custom-checkbox mr-2">
                    <input type="checkbox" class="custom-control-input item-required" id="edit_required_${Date.now()}" ${required ? 'checked' : ''}>
                    <label class="custom-control-label" for="edit_required_${Date.now()}">必填</label>
                </div>
                <button type="button" class="btn-remove" onclick="removeEditItemRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        $('#editItemList').append(itemRow);
    }

    // 移除编辑检查项目行
    function removeEditItemRow(button) {
        $(button).closest('.item-row').remove();
    }

    // 编辑模板
    function editTemplate(id) {
        // 查找模板
        const template = templates.find(t => t.id === id);

        if (!template) {
            alert('模板不存在');
            return;
        }

        // 填充表单
        $('#editTemplateId').val(template.id);
        $('#editTemplateName').val(template.name);
        $('#editTemplateDescription').val(template.description);
        $('#editTemplateCategory').val(template.category);
        $('#editIsDefault').prop('checked', template.is_default);

        // 清空检查项目列表
        $('#editItemList').empty();

        // 添加检查项目
        if (template.items && template.items.length > 0) {
            template.items.forEach(item => {
                addEditItemRow(item.name, item.description, item.required);
            });
        } else {
            // 添加一个空的检查项目
            addEditItemRow();
        }

        // 显示模态框
        $('#editTemplateModal').modal('show');
    }

    // 更新模板
    function updateTemplate() {
        // 获取表单数据
        const id = $('#editTemplateId').val();
        const name = $('#editTemplateName').val();
        const description = $('#editTemplateDescription').val();
        const category = $('#editTemplateCategory').val();
        const isDefault = $('#editIsDefault').prop('checked');

        // 获取检查项目
        const items = [];
        $('#editItemList .item-row').each(function() {
            const name = $(this).find('.item-name').val();
            const description = $(this).find('.item-description').val();
            const required = $(this).find('.item-required').prop('checked');

            if (name) {
                items.push({
                    name: name,
                    description: description,
                    required: required
                });
            }
        });

        // 验证表单
        if (!name) {
            alert('请输入模板名称');
            return;
        }

        if (!category) {
            alert('请选择模板分类');
            return;
        }

        if (items.length === 0) {
            alert('请添加至少一个检查项目');
            return;
        }

        // 构建请求数据
        const data = {
            name: name,
            description: description,
            category: category,
            items: items,
            is_default: isDefault
        };

        // 发送API请求更新模板
        $.ajax({
            url: `/api/v2/inspection-templates/${id}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(response) {
                // 关闭模态框
                $('#editTemplateModal').modal('hide');

                // 重新获取模板数据
                fetchTemplates();

                // 显示成功提示
                alert('模板更新成功');
            },
            error: function(error) {
                console.error('更新模板失败:', error);
                alert('更新模板失败: ' + (error.responseJSON?.error || '未知错误'));
            }
        });
    }

    // 删除模板
    function deleteTemplate(id) {
        if (!confirm('确定要删除此模板吗？此操作不可恢复。')) {
            return;
        }

        // 发送API请求删除模板
        $.ajax({
            url: `/api/v2/inspection-templates/${id}`,
            method: 'DELETE',
            success: function(response) {
                // 重新获取模板数据
                fetchTemplates();

                // 显示成功提示
                alert('模板删除成功');
            },
            error: function(error) {
                console.error('删除模板失败:', error);
                alert('删除模板失败: ' + (error.responseJSON?.error || '未知错误'));
            }
        });
    }

    // 使用模板
    function useTemplate(id) {
        // 查找模板
        const template = templates.find(t => t.id === id);

        if (!template) {
            alert('模板不存在');
            return;
        }

        // 将模板ID存储到本地存储
        localStorage.setItem('selectedTemplateId', id);

        // 跳转到检查记录编辑页面
        const urlParams = new URLSearchParams(window.location.search);
        const logId = urlParams.get('log_id');
        const inspectionType = urlParams.get('inspection_type');

        if (logId && inspectionType) {
            window.location.href = `/daily-management/inspections/edit/${logId}/${inspectionType}?template_id=${id}`;
        } else {
            alert('请先选择日志和检查类型');
        }
    }
</script>
{% endblock %}
