#!/usr/bin/env python3
"""
测试在线咨询修复功能
验证API路径和数据库插入问题的修复
"""

import requests
import json
from app import create_app, db
from sqlalchemy import text

def test_database_table():
    """测试数据库表结构"""
    print("🗄️  测试数据库表结构...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表是否存在
            check_table_sql = text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'online_consultations'
            """)
            result = db.session.execute(check_table_sql).fetchone()
            
            if result.count > 0:
                print("✅ online_consultations 表存在")
                
                # 检查表结构
                structure_sql = text("""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'online_consultations'
                    ORDER BY ORDINAL_POSITION
                """)
                
                columns = db.session.execute(structure_sql).fetchall()
                print("📋 表结构:")
                for col in columns:
                    nullable = "NULL" if col.IS_NULLABLE == "YES" else "NOT NULL"
                    default = f" DEFAULT {col.COLUMN_DEFAULT}" if col.COLUMN_DEFAULT else ""
                    print(f"  - {col.COLUMN_NAME}: {col.DATA_TYPE} {nullable}{default}")
                
                # 检查数据
                count_sql = text("SELECT COUNT(*) as count FROM online_consultations")
                count_result = db.session.execute(count_sql).fetchone()
                print(f"📊 表中有 {count_result.count} 条咨询数据")
                
                return True
            else:
                print("❌ online_consultations 表不存在")
                print("请先运行: python init_consultation_db.py")
                return False
                
        except Exception as e:
            print(f"❌ 数据库检查失败: {str(e)}")
            return False

def test_api_endpoint():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试API测试端点
        print("📡 测试API测试端点...")
        response = requests.get(f"{base_url}/api/consultation/test", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API测试端点响应成功")
            print(f"   消息: {data.get('message')}")
            print(f"   数据库连接: {data.get('database_connected')}")
            print(f"   表存在: {data.get('table_exists')}")
        else:
            print(f"❌ API测试端点失败: {response.status_code}")
            return False
        
        # 测试提交端点（模拟数据）
        print("\n📤 测试咨询提交端点...")
        test_data = {
            "name": "测试用户",
            "contact_type": "电话",
            "contact_value": "18373062333",
            "content": "这是一个API修复测试，验证咨询提交功能是否正常工作。"
        }
        
        response = requests.post(
            f"{base_url}/api/consultation/submit",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 咨询提交成功")
                print(f"   消息: {data.get('message')}")
                print(f"   咨询ID: {data.get('consultation_id')}")
                return True
            else:
                print(f"❌ 咨询提交失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 咨询提交请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 应用可能未运行")
        print("请先启动应用: python app.py")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_homepage_form():
    """测试首页表单"""
    print("\n🏠 测试首页表单...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问首页
        print("📄 访问首页...")
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查表单元素
            checks = [
                ("consultationForm", "咨询表单"),
                ("name", "姓名字段"),
                ("contact_type", "联系方式类型"),
                ("contact_value", "联系方式"),
                ("content", "咨询内容"),
                ("/api/consultation/submit", "正确的API路径"),
            ]
            
            results = []
            for check, description in checks:
                if check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查是否还有错误的API路径
            if "/consultation/api/submit" in content:
                print("  ❌ 发现错误的API路径: /consultation/api/submit")
                results.append(False)
            else:
                print("  ✅ 没有错误的API路径")
                results.append(True)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 首页表单检查: {success_count}/{total_count} 通过")
            return success_count == total_count
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 首页测试失败: {str(e)}")
        return False

def test_route_registration():
    """测试路由注册"""
    print("\n🛣️  测试路由注册...")
    print("-" * 50)
    
    try:
        app = create_app()
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # 查找咨询相关路由
        consultation_routes = [r for r in routes if 'consultation' in r['endpoint'] or 'consultation' in r['rule']]
        
        if consultation_routes:
            print("✅ 找到咨询相关路由:")
            for route in consultation_routes:
                methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
                print(f"  - {route['rule']} [{', '.join(methods)}] -> {route['endpoint']}")
            
            # 检查关键路由
            api_routes = [r['rule'] for r in consultation_routes if 'submit' in r['endpoint']]
            if '/api/consultation/submit' in api_routes:
                print("✅ 正确的API提交路由存在")
                return True
            else:
                print("❌ 正确的API提交路由不存在")
                return False
        else:
            print("❌ 未找到咨询相关路由")
            return False
            
    except Exception as e:
        print(f"❌ 路由测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 在线咨询修复功能测试")
    print("=" * 60)
    print("测试API路径和数据库插入问题的修复")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("数据库表结构", test_database_table()),
        ("路由注册", test_route_registration()),
        ("API端点", test_api_endpoint()),
        ("首页表单", test_homepage_form()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 修复测试结果")
    print("=" * 60)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有修复测试通过！")
        print("\n✅ 修复内容确认:")
        print("1. ✅ API路径已修正为 /api/consultation/submit")
        print("2. ✅ 数据库插入语句已优化")
        print("3. ✅ 移除了时间精度问题")
        print("4. ✅ 添加了必要的字段（status, source）")
        print("5. ✅ 前端JavaScript已更新")
        
        print("\n📋 现在可以正常使用:")
        print("1. 访问首页: http://127.0.0.1:5000")
        print("2. 滚动到底部的'联系我们'部分")
        print("3. 填写并提交在线咨询表单")
        print("4. 管理员可在后台查看咨询: /consultation/list")
        
        print("\n💡 测试建议:")
        print("1. 在首页提交一个真实的咨询测试")
        print("2. 检查数据库中是否正确保存")
        print("3. 在管理后台查看咨询记录")
        
    else:
        print("\n⚠️  部分修复测试失败")
        print("\n🔧 建议操作:")
        
        if not tests[0][1]:  # 数据库表
            print("1. 运行数据库初始化: python init_consultation_db.py")
        
        if not tests[1][1]:  # 路由注册
            print("2. 检查蓝图注册: app/__init__.py")
        
        if not tests[2][1]:  # API端点
            print("3. 检查应用是否运行: python app.py")
            print("4. 检查防火墙和端口设置")
        
        if not tests[3][1]:  # 首页表单
            print("5. 清除浏览器缓存")
            print("6. 检查模板文件: app/templates/main/index.html")
        
        print("\n7. 重启应用服务器")
        print("8. 再次运行此测试")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
