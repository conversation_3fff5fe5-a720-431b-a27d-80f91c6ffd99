#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
确保所有食材都是全局的
修复任何非全局的食材
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app import create_app, db
from sqlalchemy import text

def ensure_all_global():
    """确保所有食材都是全局的"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 检查食材全局状态...")
            
            # 查询总数
            total_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients")).fetchone()
            total_count = total_result[0] if total_result else 0
            print(f"📊 总食材数量: {total_count}")
            
            # 查询全局食材数量
            global_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients WHERE is_global = 1")).fetchone()
            global_count = global_result[0] if global_result else 0
            print(f"🌐 全局食材数量: {global_count}")
            
            # 查询非全局食材数量
            non_global_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients WHERE is_global = 0 OR is_global IS NULL")).fetchone()
            non_global_count = non_global_result[0] if non_global_result else 0
            print(f"🏫 非全局食材数量: {non_global_count}")
            
            # 查询有area_id的食材
            area_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients WHERE area_id IS NOT NULL")).fetchone()
            area_count = area_result[0] if area_result else 0
            print(f"🎯 绑定学校的食材: {area_count}")
            
            if non_global_count == 0 and area_count == 0:
                print("✅ 所有食材都已经是全局的！")
                return
            
            print(f"\n🔧 需要修复 {non_global_count + area_count} 个食材...")
            
            # 修复所有食材为全局
            result = db.session.execute(text("""
                UPDATE ingredients 
                SET is_global = 1, area_id = NULL
                WHERE is_global = 0 OR is_global IS NULL OR area_id IS NOT NULL
            """))
            
            affected_rows = result.rowcount
            db.session.commit()
            
            print(f"✅ 成功修复 {affected_rows} 个食材")
            
            # 再次验证
            final_global_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients WHERE is_global = 1")).fetchone()
            final_global_count = final_global_result[0] if final_global_result else 0
            
            final_area_result = db.session.execute(text("SELECT COUNT(*) FROM ingredients WHERE area_id IS NOT NULL")).fetchone()
            final_area_count = final_area_result[0] if final_area_result else 0
            
            print(f"\n📊 修复后状态:")
            print(f"🌐 全局食材: {final_global_count}/{total_count}")
            print(f"🎯 绑定学校的食材: {final_area_count}")
            
            if final_global_count == total_count and final_area_count == 0:
                print("🎉 所有食材现在都是全局的！")
            else:
                print("⚠️ 仍有食材未正确设置")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    print("🌐 **确保所有食材全局化工具**")
    print("="*50)
    
    ensure_all_global()
    
    print("\n✅ 现在访问 http://127.0.0.1:5000/ingredient/ 应该能看到所有食材了！")
