#!/usr/bin/env python3
"""
测试静态图片移除功能
验证首页不再显示静态图片，改为优雅的空状态
"""

import requests
from bs4 import BeautifulSoup
import re

def test_homepage_static_images():
    """测试首页静态图片移除"""
    print("🖼️  测试首页静态图片移除...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问首页
        print("📄 访问首页...")
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            soup = BeautifulSoup(content, 'html.parser')
            
            # 检查是否还有静态图片
            static_images = soup.find_all('img', src=re.compile(r'picsum\.photos'))
            
            if static_images:
                print(f"❌ 发现 {len(static_images)} 个静态图片:")
                for img in static_images:
                    print(f"  - {img.get('src')}")
                    print(f"    位置: {img.get('alt', '无描述')}")
                return False
            else:
                print("✅ 没有发现静态图片")
            
            # 检查英雄区域轮播图结构
            hero_carousel = soup.find('div', {'id': 'heroCarousel'})
            if hero_carousel:
                print("✅ 英雄区域轮播图容器存在")
                
                # 检查空状态
                empty_state = soup.find('div', {'id': 'heroCarouselEmpty'})
                if empty_state:
                    print("✅ 英雄区域空状态容器存在")
                    
                    # 检查空状态内容
                    if '智慧食堂管理系统' in str(empty_state):
                        print("✅ 空状态显示系统介绍")
                    else:
                        print("❌ 空状态内容不正确")
                        return False
                    
                    # 检查功能卡片
                    feature_cards = empty_state.find_all('div', class_=re.compile(r'bg-dark/30'))
                    if len(feature_cards) >= 4:
                        print(f"✅ 空状态包含 {len(feature_cards)} 个功能卡片")
                    else:
                        print(f"❌ 空状态功能卡片数量不足: {len(feature_cards)}")
                        return False
                else:
                    print("❌ 英雄区域空状态容器缺失")
                    return False
            else:
                print("❌ 英雄区域轮播图容器缺失")
                return False
            
            # 检查是否移除了重复的轮播图区域
            homepage_carousel_includes = content.count('homepage_carousel.html')
            if homepage_carousel_includes == 0:
                print("✅ 重复的轮播图区域已移除")
            else:
                print(f"❌ 仍有 {homepage_carousel_includes} 个重复的轮播图区域")
                return False
            
            return True
            
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_carousel_api_integration():
    """测试轮播图API集成"""
    print("\n📡 测试轮播图API集成...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试API端点
        response = requests.get(f"{base_url}/api/carousel/list", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 轮播图API响应成功")
            
            if data.get('success'):
                carousels = data.get('data', [])
                print(f"📊 获取到 {len(carousels)} 个轮播图")
                
                if carousels:
                    print("📋 轮播图列表:")
                    for i, carousel in enumerate(carousels[:3], 1):
                        title = carousel.get('title', '无标题')
                        link = carousel.get('link_url', '/')
                        active = "启用" if carousel.get('is_active') else "禁用"
                        print(f"  {i}. {title} -> {link} ({active})")
                    
                    print("✅ 有轮播图数据，首页应显示动态轮播")
                else:
                    print("⚠️  没有轮播图数据，首页应显示空状态")
                
                return True
            else:
                print(f"❌ API返回错误: {data.get('message')}")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_empty_state_styling():
    """测试空状态样式"""
    print("\n🎨 测试空状态样式...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问首页
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查空状态CSS样式
            css_checks = [
                (".hero-carousel-empty", "空状态样式"),
                ("linear-gradient", "渐变背景"),
                ("backdrop-filter: blur", "毛玻璃效果"),
                ("min-height: 400px", "最小高度"),
                ("display: flex", "弹性布局"),
                ("align-items: center", "垂直居中"),
                ("justify-content: center", "水平居中"),
            ]
            
            results = []
            for css_check, description in css_checks:
                if css_check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查响应式设计
            if "@media (max-width: 768px)" in content:
                print("  ✅ 响应式设计存在")
                results.append(True)
            else:
                print("  ❌ 响应式设计缺失")
                results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 空状态样式检查: {success_count}/{total_count} 通过")
            return success_count >= total_count * 0.8  # 80%通过即可
            
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 样式测试失败: {str(e)}")
        return False

def test_javascript_functionality():
    """测试JavaScript功能"""
    print("\n⚙️  测试JavaScript功能...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问首页
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查JavaScript功能
            js_checks = [
                ("class HeroCarousel", "英雄区域轮播图类"),
                ("loadCarouselData", "数据加载函数"),
                ("renderCarousel", "轮播图渲染函数"),
                ("showEmpty", "空状态显示函数"),
                ("initBootstrapCarousel", "Bootstrap初始化"),
                ("new HeroCarousel()", "类实例化"),
            ]
            
            results = []
            for js_check, description in js_checks:
                if js_check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            # 检查是否移除了重复的JavaScript
            homepage_carousel_js = content.count('HomepageCarousel')
            if homepage_carousel_js <= 2:  # 类定义和实例化
                print("  ✅ 没有重复的JavaScript代码")
                results.append(True)
            else:
                print(f"  ⚠️  可能有重复的JavaScript代码: {homepage_carousel_js} 次")
                results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 JavaScript功能检查: {success_count}/{total_count} 通过")
            return success_count >= total_count * 0.8  # 80%通过即可
            
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ JavaScript测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🗑️  静态图片移除功能测试")
    print("=" * 60)
    print("验证首页不再显示静态图片，改为优雅的空状态")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("静态图片移除", test_homepage_static_images()),
        ("轮播图API集成", test_carousel_api_integration()),
        ("空状态样式", test_empty_state_styling()),
        ("JavaScript功能", test_javascript_functionality()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 静态图片移除成功！")
        print("\n✅ 修复内容确认:")
        print("1. ✅ 移除了所有静态图片")
        print("2. ✅ 移除了重复的轮播图区域")
        print("3. ✅ 优化了空状态显示")
        print("4. ✅ 添加了系统功能介绍")
        print("5. ✅ 保持了轮播图功能完整")
        
        print("\n🎯 现在的显示逻辑:")
        print("1. 有轮播图数据 → 显示动态轮播图")
        print("2. 无轮播图数据 → 显示优雅的系统介绍")
        print("3. 加载过程中 → 显示加载动画")
        print("4. 所有状态都美观且有意义")
        
        print("\n💡 管理建议:")
        print("1. 在后台上传系统界面截图")
        print("2. 设置合适的标题和描述")
        print("3. 配置点击跳转链接")
        print("4. 定期更新轮播图内容")
        
    else:
        print("\n⚠️  部分测试失败")
        print("\n🔧 建议操作:")
        
        if not tests[0][1]:  # 静态图片移除
            print("1. 检查首页模板是否正确更新")
            print("2. 搜索并移除所有静态图片引用")
        
        if not tests[1][1]:  # API集成
            print("3. 检查轮播图API是否正常")
            print("4. 确认数据库中有轮播图数据")
        
        if not tests[2][1]:  # 空状态样式
            print("5. 检查CSS样式是否正确")
            print("6. 验证响应式设计")
        
        if not tests[3][1]:  # JavaScript功能
            print("7. 检查JavaScript代码")
            print("8. 确认没有重复的代码")
        
        print("\n9. 清除浏览器缓存")
        print("10. 重启应用服务器")
        print("11. 再次运行此测试")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
