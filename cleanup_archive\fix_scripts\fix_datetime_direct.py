"""
直接使用pyodbc修复DATETIME2字段精度问题

此脚本直接连接到SQL Server数据库，修复食堂日常管理、库存管理和员工管理模块相关表的DATETIME2字段精度问题。
不依赖Flask应用上下文，避免应用初始化问题。
"""
import pyodbc
import sys
import logging
from datetime import datetime

# 配置信息
SQL_SERVER = 'localhost\\SQLEXPRESS'
SQL_SERVER_DB = 'StudentsCMSSP'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"datetime_fix_direct_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_sqlserver_connection():
    """获取SQL Server数据库连接"""
    try:
        conn_str = f'DRIVER={{SQL Server}};SERVER={SQL_SERVER};DATABASE={SQL_SERVER_DB};Trusted_Connection=yes;'
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        logger.error(f"错误: 无法连接到SQL Server: {e}")
        return None

def drop_default_constraint(conn, table_name, column_name):
    """删除表中列的默认约束"""
    cursor = conn.cursor()
    try:
        # 查找默认约束名称
        cursor.execute(f"""
        SELECT dc.name
        FROM sys.default_constraints dc
        JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
        WHERE OBJECT_NAME(dc.parent_object_id) = '{table_name}' AND c.name = '{column_name}'
        """)
        
        constraint = cursor.fetchone()
        if constraint:
            constraint_name = constraint[0]
            cursor.execute(f"ALTER TABLE {table_name} DROP CONSTRAINT {constraint_name}")
            conn.commit()
            logger.info(f"已删除 {table_name} 表的 {column_name} 列的默认约束: {constraint_name}")
            return True
        else:
            logger.info(f"{table_name} 表的 {column_name} 列没有默认约束")
            return False
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"删除默认约束时出错: {e}")
        return False
    finally:
        cursor.close()

def add_default_constraint(conn, table_name, column_name, default_value="GETDATE()"):
    """为表中的列添加默认约束"""
    cursor = conn.cursor()
    try:
        constraint_name = f"DF_{table_name}_{column_name}"
        cursor.execute(f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} DEFAULT ({default_value}) FOR {column_name}")
        conn.commit()
        logger.info(f"已为 {table_name} 表的 {column_name} 列添加默认约束: {constraint_name}")
        return True
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"添加默认约束时出错: {e}")
        return False
    finally:
        cursor.close()

def alter_column_type(conn, table_name, column_name, is_nullable=False):
    """修改列的数据类型为DATETIME2(1)"""
    cursor = conn.cursor()
    try:
        nullable = "NULL" if is_nullable else "NOT NULL"
        cursor.execute(f"ALTER TABLE {table_name} ALTER COLUMN {column_name} DATETIME2(1) {nullable}")
        conn.commit()
        logger.info(f"已修改 {table_name} 表的 {column_name} 列为 DATETIME2(1) {nullable}")
        return True
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"修改列类型时出错: {e}")
        return False
    finally:
        cursor.close()

def check_column_exists(conn, table_name, column_name):
    """检查表中是否存在指定的列"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}' AND COLUMN_NAME = '{column_name}'
        """)
        count = cursor.fetchone()[0]
        return count > 0
    except pyodbc.Error as e:
        logger.error(f"检查列是否存在时出错: {e}")
        return False
    finally:
        cursor.close()

def check_table_exists(conn, table_name):
    """检查数据库中是否存在指定的表"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = '{table_name}'
        """)
        count = cursor.fetchone()[0]
        return count > 0
    except pyodbc.Error as e:
        logger.error(f"检查表是否存在时出错: {e}")
        return False
    finally:
        cursor.close()

def fix_column(conn, table_name, column_name, is_nullable=False, has_default=True):
    """修复表中的列"""
    if not check_table_exists(conn, table_name):
        logger.warning(f"表 {table_name} 不存在，跳过")
        return False
    
    if not check_column_exists(conn, table_name, column_name):
        logger.warning(f"表 {table_name} 中不存在列 {column_name}，跳过")
        return False
    
    try:
        # 1. 删除默认约束（如果有）
        if has_default:
            drop_default_constraint(conn, table_name, column_name)
        
        # 2. 修改列类型
        alter_column_type(conn, table_name, column_name, is_nullable)
        
        # 3. 重新添加默认约束（如果需要）
        if has_default:
            add_default_constraint(conn, table_name, column_name)
        
        logger.info(f"成功修复 {table_name} 表的 {column_name} 列")
        return True
    except Exception as e:
        logger.error(f"修复 {table_name} 表的 {column_name} 列时出错: {e}")
        return False

def fix_daily_management_tables(conn):
    """修复食堂日常管理模块相关表"""
    logger.info("开始修复食堂日常管理模块相关表...")
    
    # 修复 daily_logs 表
    fix_column(conn, "daily_logs", "created_at", False, True)
    fix_column(conn, "daily_logs", "updated_at", False, True)
    
    # 修复 inspection_records/daily_inspections 表
    fix_column(conn, "inspection_records", "inspection_time", False, True)
    fix_column(conn, "inspection_records", "created_at", False, True)
    fix_column(conn, "daily_inspections", "inspection_time", False, True)
    
    # 修复 dining_companions 表
    fix_column(conn, "dining_companions", "dining_time", False, True)
    fix_column(conn, "dining_companions", "created_at", False, True)
    
    # 修复 canteen_training_records 表
    fix_column(conn, "canteen_training_records", "created_at", False, True)
    
    # 修复 special_events 表
    fix_column(conn, "special_events", "created_at", False, True)
    
    # 修复 issues 表
    fix_column(conn, "issues", "created_at", False, True)
    
    logger.info("食堂日常管理模块相关表修复完成")

def fix_inventory_tables(conn):
    """修复库存管理模块相关表"""
    logger.info("开始修复库存管理模块相关表...")
    
    # 修复 warehouses 表
    fix_column(conn, "warehouses", "created_at", False, True)
    
    # 修复 inventories 表
    fix_column(conn, "inventories", "updated_at", True, True)
    
    # 修复 inventory_alerts 表
    fix_column(conn, "inventory_alerts", "created_at", False, True)
    
    # 修复 stock_ins 表
    fix_column(conn, "stock_ins", "stock_in_date", False, False)
    fix_column(conn, "stock_ins", "created_at", False, True)
    fix_column(conn, "stock_ins", "updated_at", False, True)
    
    # 修复 stock_in_items 表
    fix_column(conn, "stock_in_items", "created_at", False, True)
    
    # 修复 stock_outs 表
    fix_column(conn, "stock_outs", "stock_out_date", False, False)
    fix_column(conn, "stock_outs", "created_at", False, True)
    fix_column(conn, "stock_outs", "updated_at", False, True)
    
    # 修复 stock_out_items 表
    fix_column(conn, "stock_out_items", "created_at", False, True)
    
    # 修复 inventory_checks 表
    fix_column(conn, "inventory_checks", "created_at", False, True)
    fix_column(conn, "inventory_checks", "updated_at", False, True)
    
    logger.info("库存管理模块相关表修复完成")

def fix_employee_tables(conn):
    """修复员工管理模块相关表"""
    logger.info("开始修复员工管理模块相关表...")
    
    # 修复 employees 表
    fix_column(conn, "employees", "created_at", False, True)
    fix_column(conn, "employees", "updated_at", False, True)
    
    # 修复 health_certificates 表
    fix_column(conn, "health_certificates", "created_at", False, True)
    fix_column(conn, "health_certificates", "updated_at", False, True)
    
    # 修复 medical_examinations 表
    fix_column(conn, "medical_examinations", "created_at", False, True)
    
    # 修复 daily_health_checks 表
    fix_column(conn, "daily_health_checks", "created_at", False, True)
    
    # 修复 training_records 表
    fix_column(conn, "training_records", "created_at", False, True)
    
    logger.info("员工管理模块相关表修复完成")

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "直接修复DATETIME2字段精度问题工具")
    print("="*80)
    
    # 获取数据库连接
    conn = get_sqlserver_connection()
    if not conn:
        print("无法连接到数据库，程序退出")
        sys.exit(1)
    
    try:
        print("\n请选择要执行的操作：")
        print("1. 修复食堂日常管理模块")
        print("2. 修复库存管理模块")
        print("3. 修复员工管理模块")
        print("4. 修复所有模块")
        print("0. 退出程序")
        
        choice = input("\n请输入选项编号：").strip()
        
        if choice == '0':
            print("\n程序已退出")
            return
        elif choice == '1':
            fix_daily_management_tables(conn)
        elif choice == '2':
            fix_inventory_tables(conn)
        elif choice == '3':
            fix_employee_tables(conn)
        elif choice == '4':
            fix_daily_management_tables(conn)
            fix_inventory_tables(conn)
            fix_employee_tables(conn)
            print("\n所有模块的表已修复完成")
        else:
            print("\n无效的选项")
    
    except Exception as e:
        logger.error(f"程序执行时出错: {e}")
        print(f"\n错误: {e}")
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
