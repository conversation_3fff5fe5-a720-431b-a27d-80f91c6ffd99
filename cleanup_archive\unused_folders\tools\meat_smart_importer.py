#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肉类智能导入超级工具
专门处理D:\rou文件夹中的肉类图片，具有智能去重功能
"""

import os
import sys
import uuid
import re
from pathlib import Path
from PIL import Image, ImageOps
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

class MeatSmartImporter:
    def __init__(self, source_folder: str = "D:/rou", target_folder: str = None):
        """
        初始化肉类智能导入器

        Args:
            source_folder: 源文件夹路径
            target_folder: 目标文件夹路径
        """
        self.source_folder = Path(source_folder)

        if target_folder is None:
            self.target_folder = Path(project_root) / "app" / "static" / "uploads" / "ingredients"
        else:
            self.target_folder = Path(target_folder)

        # 图片处理参数
        self.target_size = (400, 400)
        self.quality = 85

        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

        # 统计信息
        self.stats = {
            'total_files': 0,
            'image_files': 0,
            'unique_meats': 0,
            'duplicate_files': 0,
            'processed_images': 0,
            'new_meats_created': 0,
            'images_added': 0,
            'images_replaced': 0,
            'skipped_duplicates': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }

        self.errors = []
        self.processed_names = set()  # 记录已处理的肉类名称

    def normalize_meat_name(self, filename: str) -> str:
        """
        标准化肉类名称，去除数字后缀和特殊字符

        Args:
            filename: 原始文件名（不含扩展名）

        Returns:
            str: 标准化后的肉类名称
        """
        # 去除文件名末尾的数字和常见分隔符
        # 例如: "猪肉1" -> "猪肉", "牛肉_2" -> "牛肉", "鸡肉-3" -> "鸡肉"

        # 匹配模式：末尾的数字，可能前面有分隔符
        patterns = [
            r'[_\-\s]*\d+$',      # 匹配 _1, -2, 3, _123 等
            r'[_\-\s]*\(\d+\)$',  # 匹配 (1), _(2), -(3) 等
            r'[_\-\s]*\[\d+\]$',  # 匹配 [1], _[2], -[3] 等
            r'[_\-\s]*副本\d*$',   # 匹配 副本, 副本1, 副本2 等
            r'[_\-\s]*copy\d*$',  # 匹配 copy, copy1, copy2 等
            r'[_\-\s]*备份\d*$',   # 匹配 备份, 备份1, 备份2 等
        ]

        normalized_name = filename
        for pattern in patterns:
            normalized_name = re.sub(pattern, '', normalized_name, flags=re.IGNORECASE)

        # 去除首尾空白字符
        normalized_name = normalized_name.strip()

        # 如果处理后为空，返回原始文件名
        if not normalized_name:
            normalized_name = filename

        return normalized_name

    def check_prerequisites(self) -> bool:
        """检查导入前提条件"""
        print("🔍 检查导入前提条件...")

        # 检查源文件夹
        if not self.source_folder.exists():
            print(f"❌ 源文件夹不存在: {self.source_folder}")
            return False

        # 检查目标文件夹
        self.target_folder.mkdir(parents=True, exist_ok=True)
        print(f"✅ 目标文件夹准备就绪: {self.target_folder}")

        # 检查数据库连接
        try:
            existing_count = Ingredient.query.count()
            print(f"✅ 数据库连接正常，现有食材数量: {existing_count}")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

        return True

    def scan_and_deduplicate(self) -> Dict:
        """扫描源文件夹并进行去重分析"""
        print(f"📂 扫描源文件夹: {self.source_folder}")

        # 获取所有文件
        all_files = list(self.source_folder.rglob("*"))
        files = [f for f in all_files if f.is_file()]

        # 筛选图片文件
        image_files = [f for f in files if f.suffix.lower() in self.image_extensions]

        self.stats['total_files'] = len(files)
        self.stats['image_files'] = len(image_files)

        print(f"📊 扫描完成:")
        print(f"  📄 总文件数: {len(files)}")
        print(f"  🖼️ 图片文件: {len(image_files)}")

        if not image_files:
            print("❌ 没有找到图片文件")
            return {}

        # 分析重复文件，使用标准化名称进行分组
        meat_groups = {}
        name_mapping = {}  # 记录标准化名称到原始文件名的映射

        for image_file in image_files:
            original_name = image_file.stem  # 原始文件名（不含扩展名）
            normalized_name = self.normalize_meat_name(original_name)  # 标准化名称

            if normalized_name not in meat_groups:
                meat_groups[normalized_name] = []
                name_mapping[normalized_name] = []

            meat_groups[normalized_name].append({
                'file_path': image_file,
                'file_size': image_file.stat().st_size,
                'file_name': image_file.name,
                'original_name': original_name,
                'normalized_name': normalized_name
            })

            # 记录原始名称
            if original_name not in name_mapping[normalized_name]:
                name_mapping[normalized_name].append(original_name)

        # 统计去重信息
        unique_meats = len(meat_groups)
        duplicate_count = sum(len(files) - 1 for files in meat_groups.values() if len(files) > 1)

        self.stats['unique_meats'] = unique_meats
        self.stats['duplicate_files'] = duplicate_count

        print(f"🔍 去重分析:")
        print(f"  🥩 唯一肉类: {unique_meats}")
        print(f"  🔄 重复文件: {duplicate_count}")

        # 显示重复情况示例
        duplicates = {name: files for name, files in meat_groups.items() if len(files) > 1}
        if duplicates:
            print(f"📋 智能去重示例 (前10个):")
            for i, (normalized_name, files) in enumerate(list(duplicates.items())[:10], 1):
                original_names = name_mapping[normalized_name]
                print(f"  {i}. {normalized_name} - {len(files)} 个文件")
                print(f"     原始文件名: {', '.join(original_names[:5])}")
                if len(original_names) > 5:
                    print(f"     ... 还有 {len(original_names) - 5} 个")

        return {
            'meat_groups': meat_groups,
            'name_mapping': name_mapping,
            'unique_meats': unique_meats,
            'duplicate_count': duplicate_count,
            'total_images': len(image_files)
        }

    def select_best_image(self, image_files: List[Dict]) -> Dict:
        """
        从重复的图片中选择最佳的一张

        Args:
            image_files: 同名图片文件列表

        Returns:
            Dict: 最佳图片信息
        """
        if len(image_files) == 1:
            return image_files[0]

        # 选择策略：优先选择文件大小最大的（通常质量更好）
        best_image = max(image_files, key=lambda x: x['file_size'])

        return best_image

    def find_existing_meat(self, name: str) -> Optional[Ingredient]:
        """
        查找已存在的肉类

        Args:
            name: 肉类名称

        Returns:
            Ingredient: 如果找到则返回肉类对象，否则返回None
        """
        # 精确匹配肉类名称
        existing = Ingredient.query.filter(Ingredient.name == name).first()
        return existing

    def compress_image(self, source_path: Path, target_path: Path) -> bool:
        """
        压缩图片到指定尺寸

        Args:
            source_path: 源图片路径
            target_path: 目标图片路径

        Returns:
            bool: 是否成功
        """
        try:
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)

            with Image.open(source_path) as img:
                # 转换为RGB模式（处理RGBA、P等模式）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # 使用高质量重采样算法调整大小
                img = ImageOps.fit(img, self.target_size, Image.Resampling.LANCZOS)

                # 保存压缩后的图片
                img.save(target_path, 'JPEG', quality=self.quality, optimize=True)

                return True

        except Exception as e:
            self.errors.append(f"图片压缩失败 {source_path}: {e}")
            return False

    def get_or_create_category(self, category_name: str = "肉类") -> IngredientCategory:
        """获取或创建分类"""
        category = IngredientCategory.query.filter_by(name=category_name).first()
        if not category:
            category = IngredientCategory(name=category_name)
            db.session.add(category)
            db.session.flush()  # 获取ID但不提交
        return category

    def process_single_meat(self, meat_name: str, image_files: List[Dict]) -> bool:
        """
        处理单个肉类的导入

        Args:
            meat_name: 肉类名称
            image_files: 该肉类的所有图片文件

        Returns:
            bool: 是否成功处理
        """
        try:
            # 如果已经处理过这个名称，跳过
            if meat_name in self.processed_names:
                print(f"⏭️ 跳过已处理: {meat_name}")
                self.stats['skipped_duplicates'] += 1
                return True

            # 选择最佳图片
            best_image = self.select_best_image(image_files)
            source_path = best_image['file_path']

            # 记录重复文件数量
            if len(image_files) > 1:
                duplicate_count = len(image_files) - 1
                self.stats['duplicate_files'] += duplicate_count

                # 显示原始文件名
                original_names = [img['original_name'] for img in image_files]
                print(f"🔄 发现 {meat_name} 有 {len(image_files)} 个重复文件:")
                print(f"   原始文件: {', '.join(original_names[:3])}")
                if len(original_names) > 3:
                    print(f"   ... 还有 {len(original_names) - 3} 个")
                print(f"   选择最佳: {best_image['file_name']} ({best_image['file_size']:,} 字节)")

            # 检查肉类是否已存在
            existing = self.find_existing_meat(meat_name)

            # 处理图片
            image_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}.jpg"
            target_image_path = self.target_folder / image_filename

            if not self.compress_image(source_path, target_image_path):
                return False

            if existing:
                # 肉类已存在，更新图片
                return self.update_existing_meat_image(existing, image_filename, meat_name)
            else:
                # 肉类不存在，创建新记录
                return self.create_new_meat(meat_name, image_filename)

        except Exception as e:
            error_msg = f"处理肉类失败 {meat_name}: {e}"
            self.errors.append(error_msg)
            self.stats['errors'] += 1
            print(f"❌ {error_msg}")
            return False
        finally:
            # 标记为已处理
            self.processed_names.add(meat_name)

    def update_existing_meat_image(self, existing: Ingredient, image_filename: str, meat_name: str) -> bool:
        """更新已存在肉类的图片"""
        try:
            image_path = f"uploads/ingredients/{image_filename}"

            if existing.base_image:
                print(f"🔄 更新肉类图片: {existing.name} (替换现有图片)")
                action = "replaced"
            else:
                print(f"📷 添加肉类图片: {existing.name} (原来没有图片)")
                action = "added"

            # 使用原始SQL更新图片路径
            sql = text("""
                UPDATE ingredients
                SET base_image = :base_image,
                    category = :category,
                    updated_at = GETDATE()
                WHERE id = :id
            """)

            db.session.execute(sql, {
                'base_image': image_path,
                'category': '肉类',
                'id': existing.id
            })

            self.stats['processed_images'] += 1
            if action == "added":
                self.stats['images_added'] += 1
            else:
                self.stats['images_replaced'] += 1

            print(f"✅ 图片{action}: {existing.name} [ID: {existing.id}]")
            return True

        except Exception as e:
            print(f"❌ 更新肉类图片失败 {existing.name}: {e}")
            return False

    def create_new_meat(self, meat_name: str, image_filename: str) -> bool:
        """创建新的肉类记录"""
        try:
            # 获取或创建肉类分类
            category = self.get_or_create_category("肉类")

            # 使用原始SQL创建肉类记录
            sql = text("""
                INSERT INTO ingredients
                (name, category, category_id, area_id, unit, base_image, is_global, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :category_id, :area_id, :unit, :base_image, :is_global, :status, GETDATE(), GETDATE())
            """)

            params = {
                'name': meat_name,
                'category': '肉类',
                'category_id': category.id,
                'area_id': None,  # 全局肉类
                'unit': '份',
                'base_image': f"uploads/ingredients/{image_filename}",
                'is_global': True,  # 全局肉类
                'status': 1
            }

            result = db.session.execute(sql, params)
            meat_id = result.fetchone()[0]

            self.stats['new_meats_created'] += 1
            self.stats['processed_images'] += 1

            print(f"✅ 新建肉类: {meat_name} (肉类) [ID: {meat_id}]")
            return True

        except Exception as e:
            print(f"❌ 创建肉类失败 {meat_name}: {e}")
            return False

    def batch_import(self, scan_result: Dict, batch_size: int = 50) -> Dict:
        """
        批量导入肉类

        Args:
            scan_result: 扫描结果
            batch_size: 批次大小

        Returns:
            Dict: 导入报告
        """
        self.stats['start_time'] = datetime.now()

        meat_groups = scan_result['meat_groups']
        total_meats = len(meat_groups)

        print(f"🚀 开始批量导入肉类...")
        print(f"📊 待处理肉类: {total_meats} 种")

        # 分批处理
        meat_items = list(meat_groups.items())
        batch_count = (total_meats + batch_size - 1) // batch_size

        for batch_num in range(batch_count):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_meats)
            batch_items = meat_items[start_idx:end_idx]

            print(f"\n📦 处理批次 {batch_num + 1}/{batch_count} ({len(batch_items)} 个肉类)")

            for i, (meat_name, image_files) in enumerate(batch_items):
                progress = ((batch_num * batch_size + i + 1) / total_meats) * 100
                print(f"[{progress:.1f}%] ", end="")

                self.process_single_meat(meat_name, image_files)

            # 提交当前批次
            try:
                db.session.commit()
                print(f"✅ 批次 {batch_num + 1} 提交成功")
            except Exception as e:
                db.session.rollback()
                error_msg = f"批次 {batch_num + 1} 提交失败: {e}"
                self.errors.append(error_msg)
                print(f"❌ {error_msg}")

        self.stats['end_time'] = datetime.now()

        # 生成报告
        return self.generate_report()

    def generate_report(self) -> Dict:
        """生成导入报告"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']

        # 计算总处理成功数
        total_success = (self.stats['new_meats_created'] +
                        self.stats['images_added'] +
                        self.stats['images_replaced'])

        report = {
            'summary': {
                'total_files_scanned': self.stats['total_files'],
                'image_files_found': self.stats['image_files'],
                'unique_meats_found': self.stats['unique_meats'],
                'duplicate_files_detected': self.stats['duplicate_files'],
                'images_processed': self.stats['processed_images'],
                'new_meats_created': self.stats['new_meats_created'],
                'images_added_to_existing': self.stats['images_added'],
                'images_replaced_for_existing': self.stats['images_replaced'],
                'skipped_duplicates': self.stats['skipped_duplicates'],
                'total_successful_operations': total_success,
                'errors_occurred': self.stats['errors'],
                'duration_seconds': duration.total_seconds() if duration else None,
                'success_rate': (total_success / max(self.stats['unique_meats'], 1)) * 100
            },
            'errors': self.errors,
            'timestamp': datetime.now().isoformat()
        }

        # 保存报告到文件
        report_filename = f"meat_import_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            report['report_file'] = report_filename
        except Exception as e:
            print(f"⚠️ 保存报告文件失败: {e}")

        return report

    def print_import_report(self, report: Dict):
        """打印导入报告"""
        print("\n" + "="*60)
        print("🥩 **肉类智能导入报告**")
        print("="*60)

        summary = report['summary']

        print(f"📂 扫描文件总数: {summary['total_files_scanned']}")
        print(f"🖼️ 发现图片文件: {summary['image_files_found']}")
        print(f"🥩 唯一肉类种类: {summary['unique_meats_found']}")
        print(f"🔄 检测到重复文件: {summary['duplicate_files_detected']}")
        print(f"🆕 新建肉类数量: {summary['new_meats_created']}")
        print(f"📷 添加图片数量: {summary['images_added_to_existing']}")
        print(f"🔄 替换图片数量: {summary['images_replaced_for_existing']}")
        print(f"⏭️ 跳过重复处理: {summary['skipped_duplicates']}")
        print(f"✅ 总成功操作数: {summary['total_successful_operations']}")
        print(f"❌ 处理错误数量: {summary['errors_occurred']}")
        print(f"📈 成功率: {summary['success_rate']:.1f}%")

        if summary['duration_seconds']:
            print(f"⏱️ 总耗时: {summary['duration_seconds']:.2f} 秒")

        # 显示处理详情
        print(f"\n📋 **处理详情**:")
        if summary['new_meats_created'] > 0:
            print(f"  🆕 创建了 {summary['new_meats_created']} 种新肉类")
        if summary['images_added_to_existing'] > 0:
            print(f"  📷 为 {summary['images_added_to_existing']} 种已有肉类添加了图片")
        if summary['images_replaced_for_existing'] > 0:
            print(f"  🔄 替换了 {summary['images_replaced_for_existing']} 种肉类的图片")
        if summary['duplicate_files_detected'] > 0:
            print(f"  🗑️ 智能去重，跳过了 {summary['duplicate_files_detected']} 个重复文件")

        if report['errors']:
            print(f"\n❌ **错误详情** ({len(report['errors'])} 个):")
            for i, error in enumerate(report['errors'][:10], 1):
                print(f"  {i}. {error}")
            if len(report['errors']) > 10:
                print(f"  ... 还有 {len(report['errors']) - 10} 个错误")

        if 'report_file' in report:
            print(f"\n💾 导入报告已保存到: {report['report_file']}")

        print("\n✅ 肉类智能导入完成！")

def main():
    """主函数"""
    print("🥩 **肉类智能导入超级工具**")
    print("="*60)
    print("专门处理D:\\rou文件夹中的肉类图片")
    print("✨ 特色功能: 智能去重，只保留最佳图片")
    print("="*60)

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 初始化导入器
        importer = MeatSmartImporter()

        # 检查前提条件
        if not importer.check_prerequisites():
            print("❌ 前提条件检查失败，退出程序")
            return

        # 扫描并去重分析
        scan_result = importer.scan_and_deduplicate()

        if not scan_result or scan_result['unique_meats'] == 0:
            print("❌ 没有找到可处理的肉类图片")
            return

        # 显示扫描结果
        print(f"\n🤔 发现 {scan_result['unique_meats']} 种唯一肉类")
        print(f"🔄 检测到 {scan_result['duplicate_count']} 个重复文件")
        print(f"📊 总图片文件: {scan_result['total_images']} 个")

        # 询问是否继续
        response = input(f"\n是否继续导入？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return

        # 询问是否指定学校区域
        area_response = input("是否指定学校区域？(y/N): ")
        area_id = None
        if area_response.lower() == 'y':
            try:
                area_id = int(input("请输入学校区域ID: "))
            except ValueError:
                print("⚠️ 无效的区域ID，将使用全局设置")
                area_id = None

        # 执行批量导入
        report = importer.batch_import(scan_result, batch_size=50)

        # 显示报告
        importer.print_import_report(report)

if __name__ == "__main__":
    main()
