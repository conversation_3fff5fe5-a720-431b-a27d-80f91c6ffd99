"""
测试导入模块

此脚本用于测试导入所需的模块，以便找出问题所在。
"""
import sys
import traceback

def test_import(module_name):
    """测试导入模块"""
    try:
        __import__(module_name)
        print(f"✓ 成功导入模块: {module_name}")
        return True
    except Exception as e:
        print(f"✗ 导入模块 {module_name} 失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*30 + "测试导入模块")
    print("="*80)
    
    # 测试导入模块
    modules = [
        'app',
        'app.models',
        'app.utils',
        'app.utils.datetime_patch',
        'app.utils.model_patch',
        'app.utils.app_patch',
        'app.utils.sqlalchemy_patch'
    ]
    
    for module_name in modules:
        test_import(module_name)
    
    print("\n" + "="*80)

if __name__ == "__main__":
    main()
