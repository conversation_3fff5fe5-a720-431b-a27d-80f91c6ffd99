#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速食材导入工具
简化版的食材导入工具，用于测试和小批量导入

使用方法:
1. 将图片文件放在指定文件夹中
2. 运行脚本自动导入
3. 支持自定义分类和学校绑定
"""

import os
import sys
import uuid
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from PIL import Image, ImageOps
except ImportError:
    print("❌ 需要安装Pillow库: pip install Pillow")
    sys.exit(1)

try:
    from app import create_app, db
    from app.models import Ingredient, IngredientCategory, AdministrativeArea
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入Flask应用模块失败: {e}")
    sys.exit(1)

class QuickIngredientImporter:
    """快速食材导入器"""

    def __init__(self, source_folder="test_images", target_size=(400, 400)):
        """
        初始化快速导入器

        Args:
            source_folder: 源文件夹路径
            target_size: 目标图片尺寸
        """
        self.source_folder = Path(source_folder)
        self.target_folder = Path(project_root) / "app" / "static" / "uploads" / "ingredients"
        self.target_size = target_size
        self.quality = 85

        # 支持的图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp'}

        # 统计信息
        self.imported_count = 0
        self.skipped_count = 0
        self.error_count = 0

        print(f"🚀 快速食材导入器初始化")
        print(f"📂 源文件夹: {self.source_folder}")
        print(f"📁 目标文件夹: {self.target_folder}")

    def setup_directories(self):
        """设置目录"""
        # 创建目标目录
        self.target_folder.mkdir(parents=True, exist_ok=True)

        # 创建测试源目录
        if not self.source_folder.exists():
            self.source_folder.mkdir(parents=True, exist_ok=True)
            print(f"✅ 已创建测试目录: {self.source_folder}")
            print(f"💡 请将图片文件放入 {self.source_folder} 目录中")
            return False

        return True

    def compress_image(self, source_path: Path, target_path: Path) -> bool:
        """压缩图片"""
        try:
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)

            with Image.open(source_path) as img:
                # 转换为RGB模式
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # 调整大小
                img = ImageOps.fit(img, self.target_size, Image.Resampling.LANCZOS)

                # 保存
                img.save(target_path, 'JPEG', quality=self.quality, optimize=True)
                return True

        except Exception as e:
            print(f"❌ 图片压缩失败 {source_path}: {e}")
            return False

    def get_or_create_category(self, category_name: str) -> IngredientCategory:
        """获取或创建分类"""
        category = IngredientCategory.query.filter_by(name=category_name).first()

        if not category:
            category = IngredientCategory(
                name=category_name,
                description=f"快速导入创建的{category_name}分类"
            )
            db.session.add(category)
            db.session.flush()
            print(f"✅ 创建新分类: {category_name}")

        return category

    def import_single_image(self, image_path: Path, ingredient_name: str,
                          category_name: str = "其他", area_id: int = None) -> bool:
        """导入单个图片 - 智能处理版本"""
        try:
            # 检查食材是否已存在
            existing = Ingredient.query.filter_by(name=ingredient_name).first()
            if existing:
                # 食材已存在，检查是否有图片
                if existing.base_image:
                    print(f"⏭️ 跳过已有图片的食材: {ingredient_name}")
                    self.skipped_count += 1
                    return True
                else:
                    # 食材存在但没有图片，添加图片
                    return self._add_image_to_existing(existing, image_path, category_name)
            else:
                # 食材不存在，创建新记录
                return self._create_new_ingredient_with_image(ingredient_name, category_name, image_path)

        except Exception as e:
            db.session.rollback()
            print(f"❌ 导入失败 {ingredient_name}: {e}")
            self.error_count += 1
            return False

    def _add_image_to_existing(self, existing: Ingredient, image_path: Path, category_name: str) -> bool:
        """为已存在的食材添加图片"""
        try:
            # 处理图片
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            image_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
            target_image_path = self.target_folder / image_filename

            if not self.compress_image(image_path, target_image_path):
                self.error_count += 1
                return False

            # 更新食材图片
            sql = text("""
                UPDATE ingredients
                SET base_image = :base_image,
                    category = :category,
                    updated_at = GETDATE()
                WHERE id = :id
            """)

            db.session.execute(sql, {
                'base_image': f"uploads/ingredients/{image_filename}",  # 修复：移除 /static/ 前缀
                'category': category_name,
                'id': existing.id
            })
            db.session.commit()

            self.imported_count += 1
            print(f"📷 添加图片: {existing.name} [ID: {existing.id}]")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ 添加图片失败 {existing.name}: {e}")
            self.error_count += 1
            return False

    def _create_new_ingredient_with_image(self, ingredient_name: str, category_name: str, image_path: Path) -> bool:
        """创建新食材并添加图片"""
        try:
            # 处理图片
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            image_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
            target_image_path = self.target_folder / image_filename

            if not self.compress_image(image_path, target_image_path):
                self.error_count += 1
                return False

            # 获取分类
            category = self.get_or_create_category(category_name)

            # 创建食材
            sql = text("""
                INSERT INTO ingredients
                (name, category, category_id, area_id, unit, base_image, is_global, status, created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category, :category_id, :area_id, :unit, :base_image, :is_global, :status, GETDATE(), GETDATE())
            """)

            params = {
                'name': ingredient_name,
                'category': category_name,
                'category_id': category.id,
                'area_id': None,  # 全局食材
                'unit': '份',
                'base_image': f"uploads/ingredients/{image_filename}",  # 修复：移除 /static/ 前缀
                'is_global': True,  # 全局食材
                'status': 1
            }

            result = db.session.execute(sql, params)
            ingredient_id = result.fetchone()[0]
            db.session.commit()

            self.imported_count += 1
            print(f"✅ 新建食材: {ingredient_name} [ID: {ingredient_id}]")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ 创建食材失败 {ingredient_name}: {e}")
            self.error_count += 1
            return False

    def batch_import_from_folder(self, category_name: str = "其他", area_id: int = None):
        """从文件夹批量导入"""
        if not self.setup_directories():
            return

        # 扫描图片文件
        image_files = []
        for ext in self.image_extensions:
            image_files.extend(self.source_folder.glob(f"*{ext}"))
            image_files.extend(self.source_folder.glob(f"*{ext.upper()}"))

        if not image_files:
            print(f"❌ 在 {self.source_folder} 中未找到图片文件")
            return

        print(f"📊 发现 {len(image_files)} 个图片文件")

        # 批量导入
        for image_path in image_files:
            # 从文件名推断食材名称
            ingredient_name = image_path.stem.replace('_', ' ').replace('-', ' ').strip()

            # 如果名称太短，使用原始文件名
            if len(ingredient_name) < 2:
                ingredient_name = image_path.stem

            self.import_single_image(image_path, ingredient_name, category_name, area_id)

        # 打印统计
        print(f"\n📊 导入完成:")
        print(f"✅ 成功导入: {self.imported_count}")
        print(f"⏭️ 跳过重复: {self.skipped_count}")
        print(f"❌ 导入失败: {self.error_count}")

    def import_predefined_ingredients(self, area_id: int = None):
        """导入预定义的测试食材"""
        predefined_ingredients = [
            {"name": "西红柿", "category": "蔬菜"},
            {"name": "土豆", "category": "蔬菜"},
            {"name": "胡萝卜", "category": "蔬菜"},
            {"name": "猪肉", "category": "肉类"},
            {"name": "牛肉", "category": "肉类"},
            {"name": "鸡蛋", "category": "蛋类"},
            {"name": "大米", "category": "主食"},
            {"name": "面粉", "category": "主食"},
            {"name": "食用油", "category": "调料"},
            {"name": "盐", "category": "调料"}
        ]

        print(f"🍎 开始导入预定义食材 ({len(predefined_ingredients)} 个)")

        for item in predefined_ingredients:
            # 检查重复
            existing = Ingredient.query.filter_by(name=item["name"]).first()
            if existing:
                print(f"⏭️ 跳过已存在: {item['name']}")
                self.skipped_count += 1
                continue

            try:
                # 获取分类
                category = self.get_or_create_category(item["category"])

                # 使用原始SQL创建食材（无图片）
                sql = text("""
                    INSERT INTO ingredients
                    (name, category, category_id, area_id, unit, is_global, status, created_at, updated_at)
                    OUTPUT inserted.id
                    VALUES
                    (:name, :category, :category_id, :area_id, :unit, :is_global, :status, GETDATE(), GETDATE())
                """)

                params = {
                    'name': item["name"],
                    'category': item["category"],
                    'category_id': category.id,
                    'area_id': None,  # 设置为NULL，表示全局食材
                    'unit': '份',
                    'is_global': True,  # 设置为全局食材，所有学校都可以使用
                    'status': 1
                }

                result = db.session.execute(sql, params)
                ingredient_id = result.fetchone()[0]
                db.session.commit()

                self.imported_count += 1
                print(f"✅ 导入: {item['name']} ({item['category']}) [ID: {ingredient_id}]")

            except Exception as e:
                db.session.rollback()
                print(f"❌ 导入失败 {item['name']}: {e}")
                self.error_count += 1

        print(f"\n📊 预定义食材导入完成:")
        print(f"✅ 成功: {self.imported_count}")
        print(f"⏭️ 跳过: {self.skipped_count}")
        print(f"❌ 失败: {self.error_count}")

def main():
    """主函数"""
    print("🍎 **快速食材导入工具**")
    print("="*50)

    app = create_app()

    with app.app_context():
        importer = QuickIngredientImporter()

        # 选择导入模式
        print("\n选择导入模式:")
        print("1. 从文件夹导入图片")
        print("2. 导入预定义食材（无图片）")

        choice = input("请选择 (1/2): ").strip()

        # 询问是否指定学校
        area_id = None
        use_area = input("是否指定学校区域？(y/N): ").strip().lower()
        if use_area == 'y':
            try:
                area_id = int(input("请输入学校区域ID: ").strip())
                area = AdministrativeArea.query.get(area_id)
                if area:
                    print(f"✅ 将导入到学校: {area.name}")
                else:
                    print(f"❌ 未找到ID为 {area_id} 的学校")
                    return
            except ValueError:
                print("❌ 无效的区域ID")
                return

        if choice == "1":
            category = input("请输入分类名称 (默认: 其他): ").strip() or "其他"
            importer.batch_import_from_folder(category, area_id)
        elif choice == "2":
            importer.import_predefined_ingredients(area_id)
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
