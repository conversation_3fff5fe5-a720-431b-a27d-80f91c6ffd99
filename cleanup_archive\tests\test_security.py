#!/usr/bin/env python3
"""
安全防护测试脚本
用于验证安全中间件是否正常工作
"""

import requests
import time
import sys

def test_rate_limiting():
    """测试速率限制"""
    print("🔍 测试速率限制...")
    
    url = "http://localhost:5000"
    
    # 快速发送多个请求
    success_count = 0
    blocked_count = 0
    
    for i in range(60):  # 发送60个请求
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                success_count += 1
            elif response.status_code == 429:  # Too Many Requests
                blocked_count += 1
                print(f"  ✅ 第{i+1}个请求被速率限制阻止 (429)")
            else:
                print(f"  ⚠️ 第{i+1}个请求返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 第{i+1}个请求失败: {e}")
        
        # 短暂延迟
        time.sleep(0.1)
    
    print(f"  📊 成功请求: {success_count}, 被阻止请求: {blocked_count}")
    return blocked_count > 0

def test_suspicious_user_agent():
    """测试可疑User-Agent检测"""
    print("🔍 测试可疑User-Agent检测...")
    
    url = "http://localhost:5000"
    suspicious_agents = [
        "nmap",
        "masscan", 
        "sqlmap",
        "nikto",
        "Mozilla/5.0 (compatible; Nmap Scripting Engine)"
    ]
    
    blocked_count = 0
    
    for agent in suspicious_agents:
        try:
            headers = {'User-Agent': agent}
            response = requests.get(url, headers=headers, timeout=5)
            
            if response.status_code == 403:
                print(f"  ✅ 可疑User-Agent被阻止: {agent}")
                blocked_count += 1
            else:
                print(f"  ⚠️ 可疑User-Agent未被阻止: {agent} (状态码: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 请求失败: {e}")
    
    return blocked_count > 0

def test_suspicious_paths():
    """测试可疑路径检测"""
    print("🔍 测试可疑路径检测...")
    
    base_url = "http://localhost:5000"
    suspicious_paths = [
        "/admin",
        "/wp-admin", 
        "/phpmyadmin",
        "/.env",
        "/config",
        "/backup"
    ]
    
    blocked_count = 0
    
    for path in suspicious_paths:
        try:
            url = base_url + path
            response = requests.get(url, timeout=5)
            
            if response.status_code == 404:
                print(f"  ✅ 可疑路径被阻止: {path}")
                blocked_count += 1
            else:
                print(f"  ⚠️ 可疑路径未被阻止: {path} (状态码: {response.status_code})")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ 请求失败: {e}")
    
    return blocked_count > 0

def test_large_request():
    """测试大请求阻止"""
    print("🔍 测试大请求阻止...")
    
    url = "http://localhost:5000"
    
    # 创建一个大的POST数据 (超过16MB)
    large_data = "x" * (17 * 1024 * 1024)  # 17MB
    
    try:
        response = requests.post(url, data=large_data, timeout=10)
        
        if response.status_code == 413:  # Request Entity Too Large
            print("  ✅ 大请求被成功阻止")
            return True
        else:
            print(f"  ⚠️ 大请求未被阻止 (状态码: {response.status_code})")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
        return False

def test_security_headers():
    """测试安全响应头"""
    print("🔍 测试安全响应头...")
    
    url = "http://localhost:5000"
    
    try:
        response = requests.get(url, timeout=5)
        
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options', 
            'X-XSS-Protection',
            'Referrer-Policy'
        ]
        
        found_headers = 0
        
        for header in security_headers:
            if header in response.headers:
                print(f"  ✅ 安全头存在: {header} = {response.headers[header]}")
                found_headers += 1
            else:
                print(f"  ⚠️ 安全头缺失: {header}")
        
        return found_headers >= len(security_headers) // 2
        
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🛡️ 开始安全防护测试...\n")
    
    tests = [
        ("速率限制", test_rate_limiting),
        ("可疑User-Agent检测", test_suspicious_user_agent),
        ("可疑路径检测", test_suspicious_paths),
        ("大请求阻止", test_large_request),
        ("安全响应头", test_security_headers)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
        
        # 测试间隔
        time.sleep(1)
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests >= total_tests * 0.8:  # 80%通过率
        print("🎉 安全防护系统运行良好！")
        return 0
    else:
        print("⚠️ 安全防护系统需要调整")
        return 1

if __name__ == "__main__":
    sys.exit(main())
