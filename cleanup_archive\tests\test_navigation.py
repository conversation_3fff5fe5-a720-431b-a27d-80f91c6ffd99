#!/usr/bin/env python3
"""
测试导航栏结构优化
验证食堂日常管理是否已经移动到首页子菜单中
"""

from datetime import datetime
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_menu_config():
    """测试菜单配置"""
    print("=== 测试菜单配置 ===")

    try:
        from app.utils.menu import MENU_CONFIG

        # 查找首页菜单
        home_menu = None
        daily_management_menu = None

        for menu_item in MENU_CONFIG:
            if menu_item['id'] == 'home':
                home_menu = menu_item
            elif menu_item['id'] == 'daily_management':
                daily_management_menu = menu_item

        # 检查首页菜单
        if home_menu:
            print(f"✓ 找到首页菜单: {home_menu['name']}")

            if 'children' in home_menu and home_menu['children']:
                print(f"✓ 首页有子菜单，数量: {len(home_menu['children'])}")

                # 检查子菜单项
                for child in home_menu['children']:
                    print(f"  - {child['name']} ({child['id']})")
                    if child['id'] == 'daily_management':
                        print(f"    ✓ 食堂日常管理已移动到首页子菜单")
            else:
                print("✗ 首页没有子菜单")
        else:
            print("✗ 未找到首页菜单")

        # 检查是否还有独立的食堂日常管理菜单
        if daily_management_menu:
            print("⚠️  仍然存在独立的食堂日常管理菜单")
        else:
            print("✓ 独立的食堂日常管理菜单已移除")

        return True

    except Exception as e:
        print(f"✗ 菜单配置测试失败: {str(e)}")
        return False

    print()

def test_menu_structure():
    """测试完整菜单结构"""
    print("=== 测试完整菜单结构 ===")

    try:
        from app.utils.menu import MENU_CONFIG

        print("当前菜单结构:")
        for i, menu_item in enumerate(MENU_CONFIG, 1):
            print(f"{i}. {menu_item['name']} ({menu_item['id']})")

            if 'children' in menu_item and menu_item['children']:
                for j, child in enumerate(menu_item['children'], 1):
                    print(f"   {i}.{j} {child['name']} ({child['id']})")

        return True

    except Exception as e:
        print(f"✗ 菜单结构测试失败: {str(e)}")
        return False

    print()

def test_navigation_template():
    """测试导航栏模板"""
    print("=== 测试导航栏模板 ===")

    template_file = 'app/templates/base.html'

    if os.path.exists(template_file):
        print(f"✓ 导航栏模板存在: {template_file}")

        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查关键功能
        features = {
            'menu_item.children': '子菜单支持',
            'dropdown-toggle': '下拉菜单',
            'dropdown-menu': '下拉菜单容器',
            'menu_item.url': '菜单链接支持',
            'get_url(menu_item)': 'URL参数支持'
        }

        print("导航栏模板功能检查:")
        for feature, description in features.items():
            if feature in content:
                print(f"  ✓ {description}")
            else:
                print(f"  ✗ {description}")
    else:
        print(f"✗ 导航栏模板不存在: {template_file}")
        return False

    print()
    return True

def test_url_helper():
    """测试URL辅助函数"""
    print("=== 测试URL辅助函数 ===")

    try:
        from app.utils.menu import get_url

        # 测试URL生成
        test_menu_item = {
            'url': 'daily_management.edit_log',
            'url_params': {'date_str': 'today'}
        }

        print("✓ URL辅助函数导入成功")
        print(f"✓ 测试菜单项: {test_menu_item}")

        return True

    except Exception as e:
        print(f"✗ URL辅助函数测试失败: {str(e)}")
        return False

    print()

def main():
    """主测试函数"""
    print("导航栏结构优化验证")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 运行所有测试
    tests = [
        test_menu_config,
        test_menu_structure,
        test_navigation_template,
        test_url_helper
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("=" * 50)
    print(f"测试完成！通过: {passed}/{total}")
    print()

    if passed == total:
        print("🎉 导航栏结构优化成功！")
        print()
        print("优化结果：")
        print("✅ 食堂日常管理已移动到首页子菜单")
        print("✅ 移除了独立的食堂日常管理菜单")
        print("✅ 导航栏支持子菜单结构")
        print("✅ 保持了原有的功能完整性")
        print()
        print("新的导航结构：")
        print("📁 首页")
        print("  ├── 仪表盘")
        print("  └── 食堂日常管理")
        print("📁 周菜单管理")
        print("📁 采购管理")
        print("📁 库存管理")
        print("📁 ...")
    else:
        print("❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
