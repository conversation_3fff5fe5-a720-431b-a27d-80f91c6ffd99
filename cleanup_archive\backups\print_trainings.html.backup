{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print-styles.css') }}">
<style nonce="{{ csp_nonce }}">
    body {
        font-size: 14pt;
    }
    
    .section-title {
        font-size: 16pt;
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 10px;
        border-bottom: 1px solid #4e73df;
        padding-bottom: 5px;
        color: #4e73df;
    }
    
    .info-row {
        margin-bottom: 10px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .print-page {
        padding: 20mm;
        margin-bottom: 20mm;
        border: 1px solid #ddd;
        background: #fff;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        body {
            font-size: 12pt;
        }
        
        .section-title {
            font-size: 14pt;
        }
        
        .print-page {
            padding: 0;
            margin: 0;
            border: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 打印控制按钮 -->
    <div class="no-print mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">打印预览 - 培训记录</h6>
                <div>
                    <a href="{{ url_for('daily_management.trainings', log_id=log.id) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left mr-1"></i> 返回培训记录
                    </a>
                    <button onclick="window.print()" class="btn btn-primary btn-sm">
                        <i class="fas fa-print mr-1"></i> 打印
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-1"></i> 打印预览模式。点击"打印"按钮开始打印，或使用浏览器的打印功能（Ctrl+P）。
                </div>
            </div>
        </div>
    </div>

    <!-- 打印内容 -->
    <div class="print-preview">
        <!-- 第一页：培训记录列表 -->
        <div class="print-page avoid-break">
            <div class="print-page-indicator no-print">第1页</div>
            
            <!-- 页眉 -->
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂培训记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <!-- 培训记录列表 -->
            <div class="section-title">培训记录列表</div>
            {% if trainings %}
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th width="5%">序号</th>
                        <th width="30%">培训主题</th>
                        <th width="15%">培训讲师</th>
                        <th width="15%">培训时间</th>
                        <th width="15%">培训地点</th>
                        <th width="10%">培训时长</th>
                        <th width="10%">参训人数</th>
                    </tr>
                </thead>
                <tbody>
                    {% for training in trainings %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ training.training_topic }}</td>
                        <td>{{ training.trainer }}</td>
                        <td>{{ training.training_time|safe_datetime('%H:%M') }}</td>
                        <td>{{ training.location or '-' }}</td>
                        <td>{{ training.duration or 0 }} 分钟</td>
                        <td>{{ training.attendees_count or 0 }} 人</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="text-muted">暂无培训记录</p>
            {% endif %}
        </div>
        
        <!-- 培训记录详情页 -->
        {% for training in trainings %}
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">培训记录 {{ loop.index }}</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 培训记录详情</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">{{ training.training_topic }}</div>
            
            <div class="row info-row">
                <div class="col-md-6">
                    <span class="info-label">培训讲师：</span> {{ training.trainer }}
                </div>
                <div class="col-md-6">
                    <span class="info-label">培训时间：</span> {{ training.training_time|safe_datetime('%Y-%m-%d %H:%M') }}
                </div>
            </div>
            
            <div class="row info-row">
                <div class="col-md-4">
                    <span class="info-label">培训地点：</span> {{ training.location or '未填写' }}
                </div>
                <div class="col-md-4">
                    <span class="info-label">培训时长：</span> {{ training.duration or 0 }} 分钟
                </div>
                <div class="col-md-4">
                    <span class="info-label">参训人数：</span> {{ training.attendees_count or 0 }} 人
                </div>
            </div>
            
            <div class="section-title">培训内容摘要</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ training.content_summary or '未填写' }}</p>
                </div>
            </div>
            
            <div class="section-title">效果评估</div>
            <div class="row info-row">
                <div class="col-md-12">
                    <p>{{ training.effectiveness_evaluation or '未填写' }}</p>
                </div>
            </div>
            
            {% if training.photo_paths %}
            <div class="section-title">培训照片</div>
            <div class="row">
                {% for path in training.photo_paths.split(';') %}
                <div class="col-md-4 mb-3">
                    <img src="{{ path }}" alt="培训照片" class="img-fluid img-thumbnail">
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        <!-- 签名区域 -->
        <div class="print-page avoid-break page-break">
            <div class="print-page-indicator no-print">签名页</div>
            
            <div class="text-center mb-4">
                <h1>{{ school.name }} - 食堂培训记录</h1>
                <p class="lead">日期：{{ log.log_date|safe_datetime('%Y年%m月%d日') }}</p>
            </div>
            
            <div class="section-title">签名确认</div>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <p>培训讲师签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>食堂负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <p>学校负责人签名：</p>
                        <div class="signature-line"></div>
                        <p class="mt-3">日期：_______________</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <p>（本文档由系统自动生成，打印后有效）</p>
            </div>
        </div>
    </div>
</div>

<!-- 打印控制按钮（固定在右下角） -->
<div class="print-controls no-print">
    <button onclick="window.print()"><i class="fas fa-print mr-1"></i> 打印</button>
    <button onclick="window.location.href='{{ url_for('daily_management.trainings', log_id=log.id) }}'"><i class="fas fa-times mr-1"></i> 取消</button>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 打印预览初始化
        $('.print-page').each(function(index) {
            $(this).find('.print-page-indicator').text('第' + (index + 1) + '页');
        });
    });
</script>
{% endblock %}
