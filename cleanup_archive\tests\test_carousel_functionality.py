#!/usr/bin/env python3
"""
首页轮播图功能测试脚本
测试完整的轮播图管理功能
"""

import requests
import json
import time

def test_carousel_api():
    """测试轮播图API功能"""
    print("🧪 测试轮播图API功能...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试API端点
        print("📡 测试API端点...")
        response = requests.get(f"{base_url}/api/carousel/list", timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"API响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                if data.get('success'):
                    carousels = data.get('data', [])
                    print(f"✅ API测试成功！获取到 {len(carousels)} 个轮播图")
                    
                    for i, carousel in enumerate(carousels, 1):
                        print(f"  {i}. {carousel.get('title')} (排序: {carousel.get('sort_order')})")
                    
                    return True, carousels
                else:
                    print(f"❌ API返回错误: {data.get('message')}")
                    return False, []
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {response.text}")
                return False, []
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False, []
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 应用可能未运行")
        print("请先启动应用: python app.py")
        return False, []
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False, []

def test_admin_pages():
    """测试管理页面访问"""
    print("\n🔍 测试管理页面访问...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试页面列表
    pages = [
        ("/admin/carousel", "轮播图管理列表"),
        ("/admin/carousel/create", "创建轮播图页面"),
    ]
    
    results = []
    
    for url, name in pages:
        try:
            print(f"📄 测试 {name}...")
            response = requests.get(f"{base_url}{url}", timeout=10, allow_redirects=False)
            
            if response.status_code == 200:
                print(f"  ✅ {name} 访问成功")
                results.append(True)
            elif response.status_code in [302, 301]:
                print(f"  ⚠️  {name} 重定向到登录页面（需要登录）")
                results.append(True)  # 重定向是正常的，说明路由存在
            else:
                print(f"  ❌ {name} 访问失败: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"  ❌ {name} 测试异常: {str(e)}")
            results.append(False)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 管理页面测试结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def test_homepage_integration():
    """测试首页集成"""
    print("\n🏠 测试首页轮播图集成...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        print("📄 访问首页...")
        response = requests.get(base_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查轮播图组件是否存在
            checks = [
                ("homepage_carousel.html", "轮播图组件包含"),
                ("homepageCarousel", "轮播图容器"),
                ("carousel-inner", "轮播图内容区域"),
                ("carousel-indicators", "轮播图指示器"),
                ("/api/carousel/list", "API调用"),
            ]
            
            results = []
            for check, description in checks:
                if check in content:
                    print(f"  ✅ {description} 存在")
                    results.append(True)
                else:
                    print(f"  ❌ {description} 缺失")
                    results.append(False)
            
            success_count = sum(results)
            total_count = len(results)
            
            print(f"\n📊 首页集成检查: {success_count}/{total_count} 通过")
            return success_count == total_count
        else:
            print(f"❌ 首页访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 首页测试异常: {str(e)}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n💾 测试数据库连接...")
    print("-" * 50)
    
    try:
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        
        with app.app_context():
            # 检查表是否存在
            check_table_sql = text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'homepage_carousel'
            """)
            
            result = db.session.execute(check_table_sql).fetchone()
            
            if result.count > 0:
                print("✅ 轮播图表存在")
                
                # 检查数据
                count_sql = text("SELECT COUNT(*) as count FROM homepage_carousel")
                count_result = db.session.execute(count_sql).fetchone()
                print(f"✅ 表中有 {count_result.count} 条数据")
                
                # 检查表结构
                structure_sql = text("""
                    SELECT COLUMN_NAME, DATA_TYPE 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'homepage_carousel'
                    ORDER BY ORDINAL_POSITION
                """)
                
                columns = db.session.execute(structure_sql).fetchall()
                print(f"✅ 表结构包含 {len(columns)} 个字段")
                
                return True
            else:
                print("❌ 轮播图表不存在")
                print("请先运行: python init_carousel_database.py")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def test_file_upload_directory():
    """测试文件上传目录"""
    print("\n📁 测试文件上传目录...")
    print("-" * 50)
    
    try:
        import os
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            upload_dir = os.path.join(app.static_folder, 'uploads', 'carousel')
            
            if os.path.exists(upload_dir):
                print(f"✅ 上传目录存在: {upload_dir}")
                
                # 检查目录权限
                if os.access(upload_dir, os.W_OK):
                    print("✅ 目录可写")
                else:
                    print("❌ 目录不可写")
                    return False
                
                # 列出现有文件
                files = os.listdir(upload_dir)
                print(f"📄 目录中有 {len(files)} 个文件")
                
                return True
            else:
                print(f"❌ 上传目录不存在: {upload_dir}")
                print("请先运行: python init_carousel_database.py")
                return False
                
    except Exception as e:
        print(f"❌ 目录检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 首页轮播图功能完整测试")
    print("=" * 60)
    print("测试轮播图管理系统的各项功能")
    print("=" * 60)
    
    # 测试结果收集
    test_results = []
    
    # 1. 测试数据库连接
    db_ok = test_database_connection()
    test_results.append(("数据库连接", db_ok))
    
    # 2. 测试文件上传目录
    dir_ok = test_file_upload_directory()
    test_results.append(("上传目录", dir_ok))
    
    # 3. 测试API功能
    api_ok, carousels = test_carousel_api()
    test_results.append(("API功能", api_ok))
    
    # 4. 测试管理页面
    admin_ok = test_admin_pages()
    test_results.append(("管理页面", admin_ok))
    
    # 5. 测试首页集成
    homepage_ok = test_homepage_integration()
    test_results.append(("首页集成", homepage_ok))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！轮播图功能已完全就绪")
        print("\n📋 现在可以：")
        print("1. 登录系统管理轮播图: http://127.0.0.1:5000/admin/carousel")
        print("2. 查看首页轮播效果: http://127.0.0.1:5000")
        print("3. 上传自定义轮播图片")
        print("4. 调整轮播图排序和状态")
    else:
        print("\n⚠️  部分测试失败，请检查相关问题")
        print("\n🔧 建议操作：")
        if not db_ok or not dir_ok:
            print("1. 运行数据库初始化: python init_carousel_database.py")
        if not api_ok or not admin_ok:
            print("2. 检查应用是否正常启动")
            print("3. 检查路由注册是否正确")
        if not homepage_ok:
            print("4. 检查首页模板集成")
        print("5. 重启应用服务器")
        print("6. 再次运行此测试")

if __name__ == '__main__':
    main()
