#!/usr/bin/env python3
"""
测试供应商学校绑定功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, SupplierSchoolRelation, AdministrativeArea, SupplierCategory
from datetime import datetime, date

def test_supplier_school_binding():
    """测试供应商学校绑定功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 供应商学校绑定功能测试 ===\n")
        
        # 1. 检查数据库表结构
        print("1. 检查数据库表结构...")
        try:
            # 检查供应商表
            suppliers_count = Supplier.query.count()
            print(f"   供应商表记录数: {suppliers_count}")
            
            # 检查学校表
            schools_count = AdministrativeArea.query.filter_by(level=3).count()
            print(f"   学校记录数: {schools_count}")
            
            # 检查供应商-学校关系表
            relations_count = SupplierSchoolRelation.query.count()
            print(f"   供应商-学校关系记录数: {relations_count}")
            
            print("   ✅ 数据库表结构正常\n")
            
        except Exception as e:
            print(f"   ❌ 数据库表结构检查失败: {e}\n")
            return False
        
        # 2. 检查表单字段
        print("2. 检查表单字段...")
        try:
            from app.forms.supplier import SupplierForm
            form = SupplierForm()
            
            # 检查是否有学校绑定相关字段
            required_fields = ['area_id', 'contract_number', 'start_date', 'end_date', 'relation_status', 'notes']
            missing_fields = []
            
            for field in required_fields:
                if not hasattr(form, field):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"   ❌ 缺少字段: {missing_fields}")
                return False
            else:
                print("   ✅ 表单字段完整")
                
        except Exception as e:
            print(f"   ❌ 表单字段检查失败: {e}\n")
            return False
        
        # 3. 测试创建供应商和学校关系
        print("\n3. 测试创建供应商和学校关系...")
        try:
            # 获取第一个学校
            school = AdministrativeArea.query.filter_by(level=3).first()
            if not school:
                print("   ❌ 没有找到学校数据")
                return False
            
            # 获取第一个供应商分类
            category = SupplierCategory.query.first()
            
            # 创建测试供应商
            test_supplier_name = f"测试供应商_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            supplier = Supplier(
                name=test_supplier_name,
                category_id=category.id if category else None,
                contact_person="测试联系人",
                phone="***********",
                email="<EMAIL>",
                address="测试地址",
                business_license="测试营业执照号",
                rating=3.0,
                status=1
            )
            db.session.add(supplier)
            db.session.flush()  # 获取supplier.id
            
            # 创建供应商-学校关系
            contract_number = f"C{datetime.now().strftime('%Y%m%d')}{str(supplier.id).zfill(4)}"
            
            relation = SupplierSchoolRelation(
                supplier_id=supplier.id,
                area_id=school.id,
                contract_number=contract_number,
                start_date=date.today(),
                end_date=None,
                status=1,
                notes="测试合作关系"
            )
            db.session.add(relation)
            db.session.commit()
            
            print(f"   ✅ 成功创建供应商: {test_supplier_name}")
            print(f"   ✅ 成功创建学校关系: {school.name}")
            print(f"   ✅ 合同编号: {contract_number}")
            
            # 验证关系是否正确创建
            created_relation = SupplierSchoolRelation.query.filter_by(
                supplier_id=supplier.id,
                area_id=school.id
            ).first()
            
            if created_relation:
                print("   ✅ 供应商-学校关系验证成功")
            else:
                print("   ❌ 供应商-学校关系验证失败")
                return False
                
        except Exception as e:
            db.session.rollback()
            print(f"   ❌ 创建测试数据失败: {e}")
            return False
        
        # 4. 测试查询功能
        print("\n4. 测试查询功能...")
        try:
            # 查询供应商及其学校关系
            suppliers_with_relations = Supplier.query.join(SupplierSchoolRelation).filter(
                SupplierSchoolRelation.status == 1
            ).all()
            
            print(f"   ✅ 查询到 {len(suppliers_with_relations)} 个有学校关系的供应商")
            
            # 显示前5个供应商的学校关系
            for i, supplier in enumerate(suppliers_with_relations[:5]):
                relations = [r for r in supplier.school_relations if r.status == 1]
                school_names = [r.area.name for r in relations]
                print(f"   - {supplier.name}: {', '.join(school_names)}")
                
        except Exception as e:
            print(f"   ❌ 查询功能测试失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！供应商学校绑定功能正常工作。")
        return True

if __name__ == '__main__':
    success = test_supplier_school_binding()
    sys.exit(0 if success else 1)
