#!/usr/bin/env python3
"""
测试简化的批次号生成器功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.utils.batch_number_generator import generate_batch_number, BatchNumberGenerator

def test_simple_batch_number():
    """测试简化的批次号生成器功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 简化批次号生成器测试 ===\n")
        
        # 1. 测试基础批次号生成
        print("1. 测试基础批次号生成...")
        try:
            batch_number = generate_batch_number()
            print(f"   ✅ 生成的批次号: {batch_number}")
            
            # 检查格式
            if batch_number.startswith('PB') and len(batch_number) == 16:
                print("   ✅ 批次号格式正确")
            else:
                print(f"   ❌ 批次号格式错误，长度: {len(batch_number)}")
                return False
            
        except Exception as e:
            print(f"   ❌ 批次号生成失败: {e}")
            return False
        
        # 2. 测试多次生成的唯一性
        print("\n2. 测试多次生成的唯一性...")
        try:
            batch_numbers = []
            for i in range(5):
                bn = generate_batch_number()
                batch_numbers.append(bn)
                print(f"   - 第{i+1}次生成: {bn}")
            
            # 检查是否有重复
            if len(set(batch_numbers)) == len(batch_numbers):
                print("   ✅ 多次生成的批次号都是唯一的")
            else:
                print("   ❌ 发现重复的批次号")
                return False
            
        except Exception as e:
            print(f"   ❌ 唯一性测试失败: {e}")
            return False
        
        # 3. 测试显示名称生成
        print("\n3. 测试显示名称生成...")
        try:
            display_name = BatchNumberGenerator.generate_display_name(
                batch_number,
                "蔬菜",
                "优质蔬菜供应商"
            )
            
            print(f"   ✅ 生成的显示名称: {display_name}")
            
        except Exception as e:
            print(f"   ❌ 显示名称生成失败: {e}")
            return False
        
        # 4. 测试日期提取
        print("\n4. 测试日期提取...")
        try:
            # 检查批次号中的日期部分
            if len(batch_number) >= 10:
                date_part = batch_number[2:10]
                print(f"   ✅ 提取的日期部分: {date_part}")
                
                # 验证日期格式
                from datetime import datetime
                try:
                    date_obj = datetime.strptime(date_part, '%Y%m%d')
                    print(f"   ✅ 日期解析成功: {date_obj.strftime('%Y年%m月%d日')}")
                except ValueError:
                    print("   ❌ 日期格式错误")
                    return False
            else:
                print("   ❌ 批次号长度不足，无法提取日期")
                return False
            
        except Exception as e:
            print(f"   ❌ 日期提取测试失败: {e}")
            return False
        
        # 5. 测试兼容性参数
        print("\n5. 测试兼容性参数...")
        try:
            # 测试带参数的生成（参数会被忽略，但不会报错）
            batch_with_params = generate_batch_number(
                category_id=1,
                supplier_id=1,
                user_id=1
            )
            
            print(f"   ✅ 带参数生成的批次号: {batch_with_params}")
            
            # 检查格式一致性
            if batch_with_params.startswith('PB') and len(batch_with_params) == 16:
                print("   ✅ 带参数生成的批次号格式正确")
            else:
                print("   ❌ 带参数生成的批次号格式错误")
                return False
            
        except Exception as e:
            print(f"   ❌ 兼容性参数测试失败: {e}")
            return False
        
        # 6. 测试批次号组成部分
        print("\n6. 测试批次号组成部分...")
        try:
            print(f"   - 完整批次号: {batch_number}")
            print(f"   - 前缀: {batch_number[:2]}")
            print(f"   - 日期部分: {batch_number[2:10]}")
            print(f"   - 随机数部分: {batch_number[10:]}")
            
            # 验证随机数部分是数字
            random_part = batch_number[10:]
            if random_part.isdigit() and len(random_part) == 6:
                print("   ✅ 随机数部分格式正确")
            else:
                print("   ❌ 随机数部分格式错误")
                return False
            
        except Exception as e:
            print(f"   ❌ 批次号组成部分测试失败: {e}")
            return False
        
        # 7. 测试错误处理
        print("\n7. 测试错误处理...")
        try:
            # 测试空批次号的显示名称生成
            empty_display = BatchNumberGenerator.generate_display_name("", "测试分类", "测试供应商")
            print(f"   ✅ 空批次号的显示名称: {empty_display}")
            
            # 测试无效批次号的显示名称生成
            invalid_display = BatchNumberGenerator.generate_display_name("INVALID", "测试分类", "测试供应商")
            print(f"   ✅ 无效批次号的显示名称: {invalid_display}")
            
        except Exception as e:
            print(f"   ❌ 错误处理测试失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！简化的批次号生成器功能正常工作。")
        print(f"✅ 批次号格式: PB + 日期(8位) + 随机数(6位)")
        print(f"✅ 示例批次号: {batch_number}")
        print(f"✅ 示例显示名称: {display_name}")
        return True

if __name__ == '__main__':
    success = test_simple_batch_number()
    sys.exit(0 if success else 1)
