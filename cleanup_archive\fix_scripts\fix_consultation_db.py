#!/usr/bin/env python3
"""
修复在线咨询数据库表
解决NTEXT类型的问题，改为NVARCHAR(MAX)
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def fix_consultation_table():
    """修复在线咨询表结构"""
    app = create_app()
    
    with app.app_context():
        try:
            print("正在检查在线咨询表...")
            
            # 检查表是否存在
            result = db.session.execute(text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'online_consultations'
            """))
            
            table_exists = result.fetchone()[0] > 0
            
            if table_exists:
                print("✓ 在线咨询表已存在")
                
                # 检查是否需要备份数据
                result = db.session.execute(text("SELECT COUNT(*) FROM online_consultations"))
                record_count = result.fetchone()[0]
                
                if record_count > 0:
                    print(f"发现 {record_count} 条现有数据")
                    print("是否要备份现有数据并重建表？(y/n): ", end="")
                    choice = input().lower().strip()
                    
                    if choice != 'y':
                        print("操作已取消")
                        return False
                    
                    # 备份数据
                    print("正在备份现有数据...")
                    backup_data = db.session.execute(text("""
                        SELECT name, contact_type, contact_value, 
                               CAST(content AS NVARCHAR(MAX)) as content,
                               status, 
                               CAST(reply_content AS NVARCHAR(MAX)) as reply_content,
                               reply_time, reply_user_id, source, 
                               ip_address, user_agent, created_at
                        FROM online_consultations
                    """)).fetchall()
                    
                    print(f"✓ 已备份 {len(backup_data)} 条数据")
                
                # 删除旧表
                print("正在删除旧表...")
                db.session.execute(text("DROP TABLE online_consultations"))
                db.session.commit()
                print("✓ 旧表已删除")
            
            # 创建新表
            print("正在创建新表...")
            create_table_sql = """
            CREATE TABLE online_consultations (
                id INT IDENTITY(1,1) PRIMARY KEY,
                name NVARCHAR(50) NOT NULL,
                contact_type NVARCHAR(20) NOT NULL DEFAULT '微信',
                contact_value NVARCHAR(100) NOT NULL,
                content NVARCHAR(MAX) NOT NULL,
                status NVARCHAR(20) NOT NULL DEFAULT '待处理',
                reply_content NVARCHAR(MAX) NULL,
                reply_time DATETIME2(1) NULL,
                reply_user_id INT NULL,
                source NVARCHAR(50) NOT NULL DEFAULT '官网首页',
                ip_address NVARCHAR(50) NULL,
                user_agent NVARCHAR(500) NULL,
                created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
                updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE()
            );
            """
            
            db.session.execute(text(create_table_sql))
            db.session.commit()
            print("✓ 新表创建成功")
            
            # 创建索引
            print("正在创建索引...")
            index_sqls = [
                "CREATE INDEX IX_online_consultations_status ON online_consultations(status);",
                "CREATE INDEX IX_online_consultations_created_at ON online_consultations(created_at);",
                "CREATE INDEX IX_online_consultations_contact_type ON online_consultations(contact_type);"
            ]
            
            for sql in index_sqls:
                db.session.execute(text(sql))
            
            db.session.commit()
            print("✓ 索引创建成功")
            
            # 创建触发器
            print("正在创建触发器...")
            trigger_sql = """
            CREATE TRIGGER TR_online_consultations_update
            ON online_consultations
            AFTER UPDATE
            AS
            BEGIN
                SET NOCOUNT ON;
                UPDATE online_consultations 
                SET updated_at = GETDATE()
                FROM online_consultations oc
                INNER JOIN inserted i ON oc.id = i.id;
            END;
            """
            
            db.session.execute(text(trigger_sql))
            db.session.commit()
            print("✓ 触发器创建成功")
            
            # 恢复备份数据
            if table_exists and record_count > 0 and 'backup_data' in locals():
                print("正在恢复备份数据...")
                
                for row in backup_data:
                    insert_sql = text("""
                        INSERT INTO online_consultations 
                        (name, contact_type, contact_value, content, status, 
                         reply_content, reply_time, reply_user_id, source, 
                         ip_address, user_agent, created_at)
                        VALUES 
                        (:name, :contact_type, :contact_value, :content, :status,
                         :reply_content, :reply_time, :reply_user_id, :source,
                         :ip_address, :user_agent, :created_at)
                    """)
                    
                    db.session.execute(insert_sql, {
                        'name': row[0],
                        'contact_type': row[1],
                        'contact_value': row[2],
                        'content': row[3],
                        'status': row[4],
                        'reply_content': row[5],
                        'reply_time': row[6],
                        'reply_user_id': row[7],
                        'source': row[8],
                        'ip_address': row[9],
                        'user_agent': row[10],
                        'created_at': row[11]
                    })
                
                db.session.commit()
                print(f"✓ 已恢复 {len(backup_data)} 条数据")
            
            # 插入测试数据（如果表是新建的）
            elif not table_exists:
                print("正在插入测试数据...")
                test_data = [
                    ('张三', '微信', 'zhangsan123', '想了解一下智慧食堂管理系统的具体功能和价格'),
                    ('李四', '电话', '13800138000', '我们学校想试用这个系统，请联系我'),
                    ('王五', '微信', 'wangwu888', '系统支持多少个学校同时使用？'),
                    ('赵六', '电话', '13900139000', '能否提供现场演示？我们是县教育局的')
                ]
                
                for data in test_data:
                    insert_sql = text("""
                        INSERT INTO online_consultations (name, contact_type, contact_value, content)
                        VALUES (:name, :contact_type, :contact_value, :content)
                    """)
                    
                    db.session.execute(insert_sql, {
                        'name': data[0],
                        'contact_type': data[1],
                        'contact_value': data[2],
                        'content': data[3]
                    })
                
                db.session.commit()
                print(f"✓ 已插入 {len(test_data)} 条测试数据")
            
            # 验证表结构
            print("\n验证表结构...")
            result = db.session.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'online_consultations'
                ORDER BY ORDINAL_POSITION
            """))
            
            print("-" * 70)
            print(f"{'字段名':<20} {'数据类型':<15} {'最大长度':<10} {'允许空值':<10}")
            print("-" * 70)
            
            for row in result:
                column_name = row[0]
                data_type = row[1]
                max_length = row[2] if row[2] else 'MAX' if data_type == 'nvarchar' else ''
                is_nullable = row[3]
                print(f"{column_name:<20} {data_type:<15} {str(max_length):<10} {is_nullable:<10}")
            
            print("-" * 70)
            
            # 测试查询
            print("\n测试查询功能...")
            result = db.session.execute(text("""
                SELECT 
                    id, name, contact_type, 
                    CASE 
                        WHEN LEN(content) > 30 
                        THEN LEFT(content, 30) + '...' 
                        ELSE content
                    END as content_preview,
                    status, created_at
                FROM online_consultations 
                ORDER BY created_at DESC
            """))
            
            records = result.fetchall()
            if records:
                print(f"✓ 查询成功，找到 {len(records)} 条记录")
                print("\n最新记录预览：")
                for record in records[:3]:
                    print(f"  ID: {record[0]}, 姓名: {record[1]}, 内容: {record[3]}")
            else:
                print("✓ 查询成功，暂无记录")
            
            return True
            
        except Exception as e:
            print(f"✗ 修复失败：{str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            return False

if __name__ == '__main__':
    print("=" * 60)
    print("在线咨询数据库表修复工具")
    print("=" * 60)
    print("此工具将修复NTEXT类型问题，改为NVARCHAR(MAX)")
    print("注意：此操作会重建表结构，请确保已备份重要数据")
    print("=" * 60)
    
    if fix_consultation_table():
        print("\n✓ 数据库表修复成功！")
        print("\n现在可以正常使用在线咨询功能了")
        print("- 访问首页查看咨询表单")
        print("- 管理员访问 /consultation/list 查看咨询管理")
    else:
        print("\n✗ 数据库表修复失败！")
        print("请检查数据库连接和权限设置")
        sys.exit(1)
