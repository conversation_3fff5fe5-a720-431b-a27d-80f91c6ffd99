#!/usr/bin/env python3
"""
测试供应商创建功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, SupplierSchoolRelation, AdministrativeArea, SupplierCategory
from sqlalchemy import text
from datetime import datetime, date

def test_supplier_creation():
    """测试供应商创建功能"""
    app = create_app()

    with app.app_context():
        print("=== 供应商创建功能测试 ===\n")

        # 1. 检查必要的数据
        print("1. 检查必要的数据...")
        try:
            # 检查学校数据
            schools = AdministrativeArea.query.filter_by(level=3).all()
            if not schools:
                print("   ❌ 没有学校数据，无法测试")
                return False

            school = schools[0]
            print(f"   ✅ 找到测试学校: {school.name}")

            # 检查供应商分类
            categories = SupplierCategory.query.all()
            category = categories[0] if categories else None
            if category:
                print(f"   ✅ 找到供应商分类: {category.name}")
            else:
                print("   ⚠️  没有供应商分类，将使用NULL")

        except Exception as e:
            print(f"   ❌ 数据检查失败: {e}")
            return False

        # 2. 测试原始SQL创建供应商
        print("\n2. 测试原始SQL创建供应商...")
        try:
            test_name = f"测试供应商_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 根据最佳实践，明确提供时间值，精确到0.1秒，去除微秒
            current_time = datetime.now().replace(microsecond=0)

            # 使用原始SQL创建供应商
            supplier_sql = text("""
                INSERT INTO suppliers
                (name, category_id, contact_person, phone, email, address,
                 business_license, tax_id, bank_name, bank_account, rating, status,
                 created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:name, :category_id, :contact_person, :phone, :email, :address,
                 :business_license, :tax_id, :bank_name, :bank_account, :rating, :status,
                 :created_at, :updated_at)
            """)

            supplier_params = {
                'name': test_name,
                'category_id': category.id if category else None,
                'contact_person': '测试联系人',
                'phone': '***********',
                'email': '<EMAIL>',
                'address': '测试地址123号',
                'business_license': 'TEST123456789',
                'tax_id': 'TAX123456789',
                'bank_name': '测试银行',
                'bank_account': '**********',
                'rating': 3.5,
                'status': 1,
                'created_at': current_time,
                'updated_at': current_time
            }

            result = db.session.execute(supplier_sql, supplier_params)
            supplier_id = result.fetchone()[0]

            print(f"   ✅ 成功创建供应商，ID: {supplier_id}")

        except Exception as e:
            db.session.rollback()
            print(f"   ❌ 创建供应商失败: {e}")
            return False

        # 3. 测试创建供应商-学校关系
        print("\n3. 测试创建供应商-学校关系...")
        try:
            # 生成合同编号
            now = datetime.now()
            contract_number = f"C{now.strftime('%Y%m%d')}{str(supplier_id).zfill(4)}"

            # 使用原始SQL创建供应商-学校关系
            relation_sql = text("""
                INSERT INTO supplier_school_relations
                (supplier_id, area_id, contract_number, start_date, end_date, status, notes,
                 created_at, updated_at)
                OUTPUT inserted.id
                VALUES
                (:supplier_id, :area_id, :contract_number, :start_date, :end_date, :status, :notes,
                 :created_at, :updated_at)
            """)

            # 处理日期格式，避免ODBC驱动程序问题
            start_date_str = date.today().strftime('%Y-%m-%d')

            relation_params = {
                'supplier_id': supplier_id,
                'area_id': school.id,
                'contract_number': contract_number,
                'start_date': start_date_str,
                'end_date': None,
                'status': 1,
                'notes': '测试合作关系',
                'created_at': current_time,
                'updated_at': current_time
            }

            relation_result = db.session.execute(relation_sql, relation_params)
            relation_id = relation_result.fetchone()[0]

            print(f"   ✅ 成功创建学校关系，ID: {relation_id}")
            print(f"   ✅ 合同编号: {contract_number}")

        except Exception as e:
            db.session.rollback()
            print(f"   ❌ 创建学校关系失败: {e}")
            return False

        # 4. 提交事务
        print("\n4. 提交事务...")
        try:
            db.session.commit()
            print("   ✅ 事务提交成功")
        except Exception as e:
            db.session.rollback()
            print(f"   ❌ 事务提交失败: {e}")
            return False

        # 5. 验证数据
        print("\n5. 验证创建的数据...")
        try:
            # 验证供应商
            created_supplier = Supplier.query.get(supplier_id)
            if created_supplier:
                print(f"   ✅ 供应商验证成功: {created_supplier.name}")
                print(f"   - 联系人: {created_supplier.contact_person}")
                print(f"   - 电话: {created_supplier.phone}")
                print(f"   - 状态: {'合作中' if created_supplier.status == 1 else '已停用'}")
            else:
                print("   ❌ 供应商验证失败")
                return False

            # 验证学校关系
            created_relation = SupplierSchoolRelation.query.get(relation_id)
            if created_relation:
                print(f"   ✅ 学校关系验证成功")
                print(f"   - 学校: {created_relation.area.name}")
                print(f"   - 合同编号: {created_relation.contract_number}")
                print(f"   - 状态: {'有效' if created_relation.status == 1 else '已终止'}")
            else:
                print("   ❌ 学校关系验证失败")
                return False

            # 验证关联关系
            supplier_relations = created_supplier.school_relations
            if supplier_relations:
                print(f"   ✅ 关联关系验证成功，共 {len(supplier_relations)} 个学校关系")
            else:
                print("   ❌ 关联关系验证失败")
                return False

        except Exception as e:
            print(f"   ❌ 数据验证失败: {e}")
            return False

        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！供应商创建和学校绑定功能正常工作。")
        print(f"✅ 创建的测试数据：供应商ID {supplier_id}，学校关系ID {relation_id}")
        return True

if __name__ == '__main__':
    success = test_supplier_creation()
    sys.exit(0 if success else 1)
