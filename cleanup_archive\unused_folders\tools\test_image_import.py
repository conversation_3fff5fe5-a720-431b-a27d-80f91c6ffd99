#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片导入和显示
验证图片是否正确保存和显示
"""

import os
import sys
from pathlib import Path
from PIL import Image

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from app import create_app, db
    from app.models import Ingredient
    from tools.quick_ingredient_importer import QuickIngredientImporter
    from sqlalchemy import text
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def create_test_image():
    """创建一个测试图片"""
    test_folder = Path("test_image_import")
    test_folder.mkdir(exist_ok=True)
    
    # 创建一个简单的测试图片
    img = Image.new('RGB', (800, 600), (255, 100, 100))  # 红色图片
    test_image_path = test_folder / "测试食材.jpg"
    img.save(test_image_path, 'JPEG', quality=95)
    
    print(f"✅ 创建测试图片: {test_image_path}")
    return test_image_path

def test_image_import_and_display():
    """测试图片导入和显示"""
    print("🧪 **测试图片导入和显示**")
    print("="*50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 创建测试图片
            test_image_path = create_test_image()
            
            # 2. 清理可能存在的测试食材
            existing = Ingredient.query.filter_by(name='测试食材').first()
            if existing:
                db.session.delete(existing)
                db.session.commit()
                print("🧹 清理已存在的测试食材")
            
            # 3. 使用快速导入器导入图片
            print("\n📷 开始导入测试图片...")
            importer = QuickIngredientImporter()
            
            success = importer.import_single_image(
                image_path=test_image_path,
                ingredient_name="测试食材",
                category_name="测试分类"
            )
            
            if not success:
                print("❌ 图片导入失败")
                return False
            
            # 4. 验证数据库记录
            print("\n🔍 验证数据库记录...")
            ingredient = Ingredient.query.filter_by(name='测试食材').first()
            
            if not ingredient:
                print("❌ 数据库中未找到测试食材")
                return False
            
            print(f"✅ 数据库记录: ID={ingredient.id}, 名称={ingredient.name}")
            print(f"📂 图片路径: {ingredient.base_image}")
            
            # 5. 验证图片文件是否存在
            if ingredient.base_image:
                # 构建完整的文件路径
                image_file_path = Path(project_root) / "app" / "static" / ingredient.base_image
                
                if image_file_path.exists():
                    file_size = image_file_path.stat().st_size
                    print(f"✅ 图片文件存在: {image_file_path}")
                    print(f"📏 文件大小: {file_size:,} 字节")
                    
                    # 检查图片尺寸
                    with Image.open(image_file_path) as img:
                        print(f"🖼️ 图片尺寸: {img.size[0]}x{img.size[1]}")
                        print(f"🎨 图片格式: {img.format}")
                else:
                    print(f"❌ 图片文件不存在: {image_file_path}")
                    return False
            else:
                print("❌ 数据库中没有图片路径")
                return False
            
            # 6. 测试网页访问路径
            print(f"\n🌐 网页访问路径:")
            web_url = f"http://127.0.0.1:5000/static/{ingredient.base_image}"
            print(f"  图片URL: {web_url}")
            print(f"  食材页面: http://127.0.0.1:5000/ingredient/")
            
            # 7. 显示模板中的使用方式
            print(f"\n📝 模板中的使用方式:")
            print(f"  {{{{ url_for('static', filename='{ingredient.base_image}') }}}}")
            
            print("\n✅ 图片导入和显示测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            return False
            
        finally:
            # 清理测试数据
            try:
                # 删除测试食材
                test_ingredient = Ingredient.query.filter_by(name='测试食材').first()
                if test_ingredient:
                    # 删除图片文件
                    if test_ingredient.base_image:
                        image_file_path = Path(project_root) / "app" / "static" / test_ingredient.base_image
                        if image_file_path.exists():
                            image_file_path.unlink()
                            print("🧹 删除测试图片文件")
                    
                    # 删除数据库记录
                    db.session.delete(test_ingredient)
                    db.session.commit()
                    print("🧹 删除测试数据库记录")
                
                # 删除测试文件夹
                test_folder = Path("test_image_import")
                if test_folder.exists():
                    import shutil
                    shutil.rmtree(test_folder)
                    print("🧹 删除测试文件夹")
                    
            except Exception as e:
                print(f"⚠️ 清理过程中出现错误: {e}")

def check_ingredients_folder():
    """检查ingredients文件夹状态"""
    print("\n📂 **检查ingredients文件夹状态**")
    print("="*50)
    
    ingredients_folder = Path(project_root) / "app" / "static" / "uploads" / "ingredients"
    
    print(f"📁 文件夹路径: {ingredients_folder}")
    print(f"📋 文件夹存在: {'是' if ingredients_folder.exists() else '否'}")
    
    if ingredients_folder.exists():
        # 列出文件夹中的文件
        files = list(ingredients_folder.glob("*"))
        print(f"📄 文件数量: {len(files)}")
        
        if files:
            print("📝 文件列表:")
            for i, file in enumerate(files[:10], 1):  # 只显示前10个
                file_size = file.stat().st_size
                print(f"  {i}. {file.name} ({file_size:,} 字节)")
            
            if len(files) > 10:
                print(f"  ... 还有 {len(files) - 10} 个文件")
        else:
            print("📭 文件夹为空")
    else:
        print("❌ 文件夹不存在")

def main():
    """主函数"""
    print("🍎 **图片导入和显示测试工具**")
    print("="*60)
    
    # 检查文件夹状态
    check_ingredients_folder()
    
    # 测试图片导入和显示
    success = test_image_import_and_display()
    
    if success:
        print("\n🎉 所有测试通过！图片导入和显示功能正常。")
        print("\n💡 **下一步建议**:")
        print("1. 访问 http://127.0.0.1:5000/ingredient/ 查看食材列表")
        print("2. 运行 python tools/ingredient_batch_importer.py 批量导入D:\\FOOT中的食材")
        print("3. 检查导入的食材是否正确显示图片")
    else:
        print("\n❌ 测试失败，请检查相关配置。")

if __name__ == "__main__":
    main()
