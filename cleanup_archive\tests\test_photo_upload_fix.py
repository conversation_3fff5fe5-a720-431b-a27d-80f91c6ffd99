#!/usr/bin/env python3
"""
测试照片上传功能修复
验证扫码上传照片的问题是否已解决
"""

from datetime import datetime
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_upload_page_access():
    """测试上传页面访问"""
    print("=== 测试上传页面访问 ===")
    
    try:
        from app import create_app
        from app.models_daily_management import DailyLog
        from app.models import AdministrativeArea
        
        app = create_app()
        with app.app_context():
            # 获取测试数据
            log = DailyLog.query.first()
            if log:
                school = AdministrativeArea.query.get(log.area_id)
                print(f"✓ 找到测试日志: ID={log.id}, 学校={school.name if school else '未知'}")
                
                # 构建测试URL
                test_urls = [
                    f"/daily-management/public/upload-inspection-photo/{log.area_id}/{log.id}/morning",
                    f"/daily-management/public/upload-inspection-photo/{log.area_id}/{log.id}/noon", 
                    f"/daily-management/public/upload-inspection-photo/{log.area_id}/{log.id}/evening"
                ]
                
                for url in test_urls:
                    print(f"✓ 测试URL: http://127.0.0.1:5000{url}")
                
                return True
            else:
                print("✗ 未找到测试日志")
                return False
                
    except Exception as e:
        print(f"✗ 页面访问测试失败: {str(e)}")
        return False

def test_template_fixes():
    """测试模板修复"""
    print("\n=== 测试模板修复 ===")
    
    template_file = 'app/templates/daily_management/public_upload_inspection_photo.html'
    
    if os.path.exists(template_file):
        print(f"✓ 模板文件存在: {template_file}")
        
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复项目
        fixes = {
            'capture="environment"': '移除强制摄像头属性',
            '点击选择照片或拍照': '更新提示文字',
            '支持相册选择、拍照、多张照片、拖拽上传': '完整功能说明',
            'file.size > 10 * 1024 * 1024': '增加文件大小限制到10MB',
            'selectedFiles.length + validFiles.length > 10': '限制最多10张照片',
            '网络连接失败，请检查网络后重试': '改进错误提示'
        }
        
        print("模板修复检查:")
        for fix, description in fixes.items():
            if fix in content:
                if fix == 'capture="environment"':
                    print(f"  ✗ {description} - 仍然存在")
                else:
                    print(f"  ✓ {description}")
            else:
                if fix == 'capture="environment"':
                    print(f"  ✓ {description} - 已移除")
                else:
                    print(f"  ✗ {description} - 未找到")
        
        return True
    else:
        print(f"✗ 模板文件不存在: {template_file}")
        return False

def test_api_fixes():
    """测试API修复"""
    print("\n=== 测试API修复 ===")
    
    api_file = 'app/routes/daily_management/public_api.py'
    
    if os.path.exists(api_file):
        print(f"✓ API文件存在: {api_file}")
        
        with open(api_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复项目
        fixes = {
            'photo_result = handle_photo_upload': '修复照片处理返回值',
            'img.convert(\'RGB\')': '图片格式转换',
            'Image.Resampling.LANCZOS': '高质量图片缩放',
            'format=\'JPEG\', quality=85, optimize=True': '统一JPEG格式和压缩',
            'db.session.rollback()': '添加事务回滚',
            'unique_filename.lower().endswith(\'.jpg\')': '统一文件扩展名'
        }
        
        print("API修复检查:")
        for fix, description in fixes.items():
            if fix in content:
                print(f"  ✓ {description}")
            else:
                print(f"  ✗ {description} - 未找到")
        
        return True
    else:
        print(f"✗ API文件不存在: {api_file}")
        return False

def test_upload_directory():
    """测试上传目录"""
    print("\n=== 测试上传目录 ===")
    
    upload_dirs = [
        'app/static/uploads/daily_management/morning',
        'app/static/uploads/daily_management/noon',
        'app/static/uploads/daily_management/evening'
    ]
    
    for upload_dir in upload_dirs:
        if os.path.exists(upload_dir):
            print(f"✓ 上传目录存在: {upload_dir}")
            files = os.listdir(upload_dir)
            print(f"  - 文件数量: {len(files)}")
        else:
            print(f"✗ 上传目录不存在: {upload_dir}")
            try:
                os.makedirs(upload_dir, exist_ok=True)
                print(f"  ✓ 已创建目录: {upload_dir}")
            except Exception as e:
                print(f"  ✗ 创建目录失败: {str(e)}")

def test_route_registration():
    """测试路由注册"""
    print("\n=== 测试路由注册 ===")
    
    try:
        from app import create_app
        
        app = create_app()
        
        # 检查关键路由
        routes = []
        for rule in app.url_map.iter_rules():
            if 'public' in rule.rule and 'upload' in rule.rule:
                routes.append(rule.rule)
        
        if routes:
            print("✓ 找到公开上传路由:")
            for route in routes:
                print(f"  - {route}")
            return True
        else:
            print("✗ 未找到公开上传路由")
            return False
            
    except Exception as e:
        print(f"✗ 路由注册测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("照片上传功能修复验证")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 运行所有测试
    tests = [
        test_upload_page_access,
        test_template_fixes,
        test_api_fixes,
        test_upload_directory,
        test_route_registration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成！通过: {passed}/{total}")
    print()
    
    if passed == total:
        print("🎉 照片上传功能修复成功！")
        print()
        print("修复内容：")
        print("✅ 移除了强制摄像头属性，支持相册选择")
        print("✅ 改进了文件验证和错误处理")
        print("✅ 统一了图片格式处理和压缩")
        print("✅ 增强了网络错误处理")
        print("✅ 优化了用户界面提示")
        print()
        print("现在支持：")
        print("📱 手机拍照")
        print("🖼️ 相册选择")
        print("🖱️ 拖拽上传")
        print("📁 多文件选择")
        print("🔄 自动压缩")
        print("⚠️ 详细错误提示")
    else:
        print("❌ 部分测试失败，请检查修复")
        print()
        print("建议检查：")
        print("1. 模板文件是否正确修改")
        print("2. API路由是否正确注册")
        print("3. 上传目录权限是否正确")
        print("4. 数据库连接是否正常")

if __name__ == "__main__":
    main()
