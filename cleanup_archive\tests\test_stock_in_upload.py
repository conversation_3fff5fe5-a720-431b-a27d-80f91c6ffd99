#!/usr/bin/env python3
"""
入库单文件上传功能测试脚本
专门测试 /stock-in/create-from-purchase-order/40 页面
"""

import os
import sys
import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

def test_stock_in_page():
    """测试入库单创建页面的文件上传功能"""
    print("🔍 测试入库单创建页面文件上传功能...")
    
    # Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    
    driver = None
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # 测试URL
        test_url = 'http://127.0.0.1:5000/stock-in/create-from-purchase-order/40'
        
        print(f"📄 访问页面: {test_url}")
        driver.get(test_url)
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 检查页面标题
        page_title = driver.title
        print(f"📋 页面标题: {page_title}")
        
        # 检查是否有CSP错误
        logs = driver.get_log('browser')
        csp_errors = [log for log in logs if 'Content Security Policy' in log.get('message', '')]
        
        if csp_errors:
            print("❌ 发现CSP错误:")
            for error in csp_errors:
                print(f"   {error['message']}")
        else:
            print("✅ 无CSP错误")
        
        # 检查关键元素是否存在
        elements_to_check = [
            ('#stockInForm', '入库表单'),
            ('#itemsTable', '入库明细表格'),
            ('.upload-doc-btn', '上传文档按钮'),
            ('#uploadModal', '上传模态框'),
            ('#dropZone', '文件拖拽区域'),
            ('#fileInput', '文件输入元素')
        ]
        
        for selector, name in elements_to_check:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                print(f"✅ 找到 {name}")
                
                # 特别检查文件输入元素
                if selector == '#fileInput':
                    is_displayed = element.is_displayed()
                    is_enabled = element.is_enabled()
                    accept_attr = element.get_attribute('accept')
                    multiple_attr = element.get_attribute('multiple')
                    
                    print(f"   文件输入详情: 显示={is_displayed}, 启用={is_enabled}")
                    print(f"   接受类型: {accept_attr}")
                    print(f"   多选: {multiple_attr is not None}")
                    
            except Exception as e:
                print(f"❌ 未找到 {name}: {e}")
        
        # 检查文件上传修复脚本是否加载
        script_loaded = driver.execute_script("""
            return typeof window.FileUploadFix !== 'undefined';
        """)
        
        if script_loaded:
            print("✅ 文件上传修复脚本已加载")
        else:
            print("❌ 文件上传修复脚本未加载")
        
        # 测试点击上传按钮
        try:
            upload_buttons = driver.find_elements(By.CSS_SELECTOR, '.upload-doc-btn')
            if upload_buttons:
                print(f"✅ 找到 {len(upload_buttons)} 个上传按钮")
                
                # 点击第一个上传按钮
                first_button = upload_buttons[0]
                driver.execute_script("arguments[0].click();", first_button)
                
                # 等待模态框出现
                try:
                    WebDriverWait(driver, 5).until(
                        EC.visibility_of_element_located((By.ID, "uploadModal"))
                    )
                    print("✅ 上传模态框成功打开")
                    
                    # 检查拖拽区域
                    drop_zone = driver.find_element(By.ID, "dropZone")
                    if drop_zone.is_displayed():
                        print("✅ 文件拖拽区域可见")
                    else:
                        print("❌ 文件拖拽区域不可见")
                        
                except TimeoutException:
                    print("❌ 上传模态框未能打开")
            else:
                print("❌ 未找到上传按钮")
                
        except Exception as e:
            print(f"❌ 测试上传按钮时出错: {e}")
        
        # 检查JavaScript错误
        js_errors = [log for log in logs if log['level'] == 'SEVERE']
        if js_errors:
            print("⚠️ 发现JavaScript错误:")
            for error in js_errors:
                print(f"   {error['message']}")
        else:
            print("✅ 无JavaScript错误")
        
        return True
        
    except WebDriverException as e:
        print(f"❌ WebDriver错误: {e}")
        print("💡 提示: 请确保已安装Chrome浏览器和ChromeDriver")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def test_page_accessibility():
    """测试页面可访问性"""
    print("\n🔍 测试页面可访问性...")
    
    try:
        test_url = 'http://127.0.0.1:5000/stock-in/create-from-purchase-order/40'
        response = requests.get(test_url, timeout=10)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面可正常访问")
            
            # 检查响应头
            csp_header = response.headers.get('Content-Security-Policy')
            if csp_header:
                print("✅ 发现CSP头")
                if 'unsafe-eval' in csp_header:
                    print("✅ CSP允许eval函数")
                else:
                    print("⚠️ CSP可能阻止eval函数")
            else:
                print("⚠️ 未发现CSP头")
                
            # 检查内容
            content = response.text
            if 'file-upload-fix.js' in content:
                print("✅ 页面包含文件上传修复脚本")
            else:
                print("❌ 页面未包含文件上传修复脚本")
                
            if 'dropZone' in content:
                print("✅ 页面包含文件拖拽区域")
            else:
                print("❌ 页面未包含文件拖拽区域")
                
            return True
        else:
            print(f"❌ 页面访问失败，状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始入库单文件上传功能测试\n")
    
    # 检查Flask应用是否运行
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        print("✅ Flask应用正在运行")
    except requests.RequestException:
        print("❌ Flask应用未运行，请先启动应用")
        print("💡 运行命令: python run.py")
        return False
    
    # 运行测试
    tests = [
        ("页面可访问性测试", test_page_accessibility),
        ("文件上传功能测试", test_stock_in_page),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print("📊 测试结果摘要")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！入库单文件上传功能正常！")
        print("\n💡 手动测试建议:")
        print("1. 访问 http://127.0.0.1:5000/stock-in/create-from-purchase-order/40")
        print("2. 点击任意食材行的'上传证明'按钮")
        print("3. 在弹出的模态框中点击拖拽区域")
        print("4. 确认文件选择对话框正常弹出")
        print("5. 选择文件后确认文件列表正确显示")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
