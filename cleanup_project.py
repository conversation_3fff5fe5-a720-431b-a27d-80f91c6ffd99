#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目文件清理工具
清理项目中的无用文件，保持项目文件夹干净
"""

import os
import shutil
import glob
from pathlib import Path
from datetime import datetime

class ProjectCleaner:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.cleanup_folder = self.project_root / "cleanup_archive"
        self.stats = {
            'moved_files': 0,
            'moved_dirs': 0,
            'freed_space': 0
        }
        
    def create_cleanup_folder(self):
        """创建清理归档文件夹"""
        if not self.cleanup_folder.exists():
            self.cleanup_folder.mkdir()
            print(f"✅ 创建清理归档文件夹: {self.cleanup_folder}")
        
    def get_file_size(self, path):
        """获取文件或文件夹大小"""
        if path.is_file():
            return path.stat().st_size
        elif path.is_dir():
            total = 0
            try:
                for item in path.rglob('*'):
                    if item.is_file():
                        total += item.stat().st_size
            except (PermissionError, OSError):
                pass
            return total
        return 0
    
    def move_to_cleanup(self, path, category="misc"):
        """移动文件到清理文件夹"""
        if not path.exists():
            return
            
        # 创建分类文件夹
        category_folder = self.cleanup_folder / category
        category_folder.mkdir(exist_ok=True)
        
        # 计算大小
        size = self.get_file_size(path)
        
        # 移动文件
        target = category_folder / path.name
        if target.exists():
            # 如果目标已存在，添加时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            stem = path.stem
            suffix = path.suffix
            target = category_folder / f"{stem}_{timestamp}{suffix}"
        
        try:
            shutil.move(str(path), str(target))
            if path.is_dir():
                self.stats['moved_dirs'] += 1
            else:
                self.stats['moved_files'] += 1
            self.stats['freed_space'] += size
            print(f"📦 移动: {path} -> {target}")
        except Exception as e:
            print(f"❌ 移动失败: {path} - {e}")
    
    def cleanup_backup_files(self):
        """清理备份文件"""
        print("\n🧹 清理备份文件...")
        
        backup_patterns = [
            "*.bak*",
            "*.backup*", 
            "*.old",
            "*.orig",
            "*.tmp",
            "*.temp",
            "*~",
            "*.log.*",
            "*_backup*",
            "*.remaining.backup",
            "*.final.backup",
            "*.critical.backup"
        ]
        
        for pattern in backup_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file() and "cleanup_archive" not in str(file_path):
                    self.move_to_cleanup(file_path, "backups")
    
    def cleanup_test_files(self):
        """清理测试文件"""
        print("\n🧪 清理测试文件...")
        
        test_files = [
            "test_*.py",
            "debug_*.py", 
            "simple_test.py",
            "quick_test*.py",
            "*_test.py",
            "test_*.html",
            "debug_*.html",
            "test_*.js"
        ]
        
        for pattern in test_files:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    self.move_to_cleanup(file_path, "tests")
    
    def cleanup_fix_scripts(self):
        """清理修复脚本"""
        print("\n🔧 清理修复脚本...")
        
        fix_patterns = [
            "fix_*.py",
            "*_fix.py", 
            "repair_*.py",
            "patch_*.py",
            "update_*.py",
            "migrate_*.py",
            "check_*.py",
            "verify_*.py",
            "analyze_*.py",
            "scan_*.py",
            "comprehensive_*.py",
            "advanced_*.py",
            "final_*.py",
            "quick_*.py",
            "simple_*.py"
        ]
        
        for pattern in fix_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    self.move_to_cleanup(file_path, "fix_scripts")
    
    def cleanup_documentation(self):
        """清理文档文件"""
        print("\n📚 清理文档文件...")
        
        doc_files = [
            "*.md",
            "*.txt", 
            "*.doc",
            "*.docx",
            "*.pdf",
            "*.ini"
        ]
        
        # 保留重要文档
        keep_docs = [
            "README.md",
            "Readme.md", 
            "requirements.txt",
            "config.py"
        ]
        
        for pattern in doc_files:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file() and file_path.name not in keep_docs:
                    self.move_to_cleanup(file_path, "documentation")
    
    def cleanup_unused_folders(self):
        """清理无用文件夹"""
        print("\n📁 清理无用文件夹...")
        
        unused_folders = [
            "AdminLTE-master",
            "bootstrap-4.6.2", 
            "indexnew",
            "node_modules",
            "archive",
            "backups",
            "sqlite_backups",
            "logs",
            "applogs",
            "appstaticimg",
            "demo_ingredients",
            "test_smart_import",
            "static",
            "templates",
            "routes",
            "models",
            "forms",
            "tools",
            "security_tools",
            "scripts"
        ]
        
        for folder_name in unused_folders:
            folder_path = self.project_root / folder_name
            if folder_path.exists() and folder_path.is_dir():
                self.move_to_cleanup(folder_path, "unused_folders")
    
    def cleanup_cache_files(self):
        """清理缓存文件"""
        print("\n🗑️ 清理缓存文件...")
        
        # Python缓存
        for pycache in self.project_root.rglob("__pycache__"):
            if pycache.is_dir():
                self.move_to_cleanup(pycache, "cache")
        
        # 其他缓存文件
        cache_patterns = [
            "*.pyc",
            "*.pyo", 
            "*.pyd",
            ".DS_Store",
            "Thumbs.db",
            "*.tmp",
            "*.cache"
        ]
        
        for pattern in cache_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    self.move_to_cleanup(file_path, "cache")
    
    def cleanup_report_files(self):
        """清理报告文件"""
        print("\n📊 清理报告文件...")
        
        report_patterns = [
            "*_report_*.json",
            "*_report_*.txt",
            "*.log",
            "csp_*.json",
            "*_analysis_*.txt"
        ]
        
        for pattern in report_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    self.move_to_cleanup(file_path, "reports")
    
    def cleanup_compressed_files(self):
        """清理压缩文件"""
        print("\n📦 清理压缩文件...")
        
        compressed_patterns = [
            "*.zip",
            "*.7z",
            "*.rar", 
            "*.tar.gz",
            "*.tar"
        ]
        
        for pattern in compressed_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    self.move_to_cleanup(file_path, "compressed")
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"
    
    def run_cleanup(self):
        """运行完整清理"""
        print("🚀 开始项目清理...")
        print("=" * 60)
        
        self.create_cleanup_folder()
        
        # 执行各种清理
        self.cleanup_backup_files()
        self.cleanup_test_files() 
        self.cleanup_fix_scripts()
        self.cleanup_documentation()
        self.cleanup_cache_files()
        self.cleanup_report_files()
        self.cleanup_compressed_files()
        self.cleanup_unused_folders()
        
        # 显示统计
        print("\n" + "=" * 60)
        print("📊 清理统计:")
        print(f"📁 移动文件夹: {self.stats['moved_dirs']}")
        print(f"📄 移动文件: {self.stats['moved_files']}")
        print(f"💾 释放空间: {self.format_size(self.stats['freed_space'])}")
        print(f"📦 归档位置: {self.cleanup_folder}")
        print("\n✅ 项目清理完成！")

if __name__ == "__main__":
    cleaner = ProjectCleaner()
    cleaner.run_cleanup()
