#!/usr/bin/env python3
"""
快速修复后台管理菜单显示问题
为轮播图和在线咨询功能添加直接访问链接
"""

def create_admin_menu_template():
    """创建管理员快速访问页面模板"""
    
    template_content = '''
<!-- 管理员快速访问页面 -->
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-cogs"></i> 系统管理快速访问
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 首页轮播图管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-images fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">首页轮播图管理</h5>
                                    <p class="card-text">管理首页轮播图片，支持多图上传和排序</p>
                                    <a href="/admin/carousel" class="btn btn-primary">
                                        <i class="fas fa-edit"></i> 管理轮播图
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 在线咨询管理 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-comments fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">在线咨询管理</h5>
                                    <p class="card-text">查看和回复用户咨询，管理留言记录</p>
                                    <a href="/consultation/list" class="btn btn-success">
                                        <i class="fas fa-reply"></i> 管理咨询
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 系统设置 -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-sliders-h fa-3x text-info mb-3"></i>
                                    <h5 class="card-title">系统设置</h5>
                                    <p class="card-text">配置系统参数和基础设置</p>
                                    <a href="/system/settings" class="btn btn-info">
                                        <i class="fas fa-cog"></i> 系统设置
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h5><i class="fas fa-info-circle"></i> 使用说明</h5>
                            <div class="alert alert-info">
                                <ul class="mb-0">
                                    <li><strong>首页轮播图管理</strong>：可以添加、编辑、删除首页的轮播图片，支持批量上传</li>
                                    <li><strong>在线咨询管理</strong>：查看用户通过首页提交的咨询和留言，可以回复和管理</li>
                                    <li><strong>系统设置</strong>：配置系统的基本参数和功能开关</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
'''
    
    return template_content

def create_quick_access_route():
    """创建快速访问路由代码"""
    
    route_code = '''
# 添加到 app/admin/routes.py 中

@system_bp.route('/quick-access')
@login_required
@admin_required
def quick_access():
    """管理员快速访问页面"""
    return render_template('admin/quick_access.html', title='系统管理快速访问')
'''
    
    return route_code

def main():
    """主函数"""
    print("🚀 后台管理菜单修复工具")
    print("=" * 60)
    
    print("📋 问题分析：")
    print("首页广告轮换（轮播图）和留言功能（在线咨询）的后台管理入口已经存在，")
    print("但可能在菜单中没有正确显示。")
    
    print("\n🔧 解决方案：")
    print("1. 直接访问管理页面URL")
    print("2. 检查用户权限配置")
    print("3. 创建快速访问页面")
    
    print("\n📍 直接访问地址：")
    print("=" * 40)
    
    print("\n🎠 首页轮播图管理：")
    print("   URL: http://127.0.0.1:5000/admin/carousel")
    print("   功能：")
    print("   - 查看所有轮播图")
    print("   - 添加新轮播图")
    print("   - 编辑现有轮播图")
    print("   - 批量上传图片")
    print("   - 启用/禁用轮播图")
    print("   - 调整显示顺序")
    
    print("\n💬 在线咨询管理：")
    print("   URL: http://127.0.0.1:5000/consultation/list")
    print("   功能：")
    print("   - 查看所有咨询记录")
    print("   - 搜索和筛选咨询")
    print("   - 回复用户咨询")
    print("   - 关闭已处理咨询")
    print("   - 导出咨询数据")
    
    print("\n🔑 权限要求：")
    print("   - 需要使用管理员账号登录")
    print("   - 角色需要具有相应的模块权限")
    print("   - carousel.view - 轮播图查看权限")
    print("   - consultation.view - 咨询查看权限")
    
    print("\n💡 如果菜单中没有显示，可能的原因：")
    print("1. 用户角色权限不足")
    print("2. 菜单可见性设置问题")
    print("3. 模块权限配置缺失")
    
    print("\n🛠️  快速解决步骤：")
    print("1. 启动应用：python run.py")
    print("2. 使用管理员账号登录")
    print("3. 直接访问上述URL地址")
    print("4. 如果无法访问，检查用户权限")
    
    print("\n" + "=" * 60)
    print("✅ 修复指南完成！")
    print("如果仍有问题，请检查：")
    print("- 应用是否正常启动")
    print("- 数据库连接是否正常")
    print("- 用户是否具有管理员权限")
    
    return True

if __name__ == '__main__':
    main()
