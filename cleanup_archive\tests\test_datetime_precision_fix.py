#!/usr/bin/env python3
"""
测试datetime精度修复的脚本
"""

import requests
import json
import sys
from datetime import datetime

def test_stock_in_creation():
    """测试入库单创建功能"""
    print("🧪 测试入库单创建功能...")
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试数据
    test_data = {
        'warehouse_id': 1,
        'purchase_order_id': 40,
        'stock_in_date': datetime.now().strftime('%Y-%m-%d'),
        'notes': '测试入库单创建 - datetime精度修复',
        'items': [
            {
                'id': 1,
                'actual_quantity': 10.0,
                'unit': 'kg'
            }
        ]
    }
    
    try:
        # 测试从采购订单创建入库单
        url = f"{base_url}/stock-in/save-from-purchase-order"
        
        print(f"📤 发送请求到: {url}")
        print(f"📋 请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        
        # 发送JSON请求
        response = requests.post(url, 
                               json=test_data, 
                               headers={'Content-Type': 'application/json'},
                               timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功")
            
            try:
                result = response.json()
                print(f"📦 JSON响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('success'):
                    print("🎉 入库单创建成功！")
                    return True
                else:
                    print(f"❌ 入库单创建失败: {result.get('message')}")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                print(f"📄 响应内容: {response.text[:500]}...")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}...")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_storage_location_query():
    """测试存储位置查询功能"""
    print("\n🧪 测试存储位置查询功能...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 测试获取存储位置列表
        url = f"{base_url}/inventory/get-storage-locations"
        params = {'warehouse_id': 1}
        
        print(f"📤 发送请求到: {url}")
        print(f"📋 请求参数: {params}")
        
        response = requests.get(url, params=params, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📦 JSON响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if isinstance(result, list):
                    print(f"✅ 成功获取 {len(result)} 个存储位置")
                    return True
                else:
                    print("❌ 响应格式不正确")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保Flask应用正在运行: python run.py")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 尝试访问一个简单的数据库查询页面
        url = f"{base_url}/warehouse"
        
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            print("✅ 数据库连接正常")
            return True
        elif response.status_code == 302:
            print("⚠️ 需要登录，但数据库连接正常")
            return True
        else:
            print(f"❌ 数据库连接可能有问题，状态码: {response.status_code}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始datetime精度修复测试\n")
    
    # 检查服务器连接
    if not test_server_connection():
        return False
    
    # 检查数据库连接
    if not test_database_connection():
        return False
    
    # 运行测试
    tests = [
        ("存储位置查询测试", test_storage_location_query),
        ("入库单创建测试", test_stock_in_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print("📊 测试结果摘要")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！datetime精度问题已修复！")
        print("\n💡 修复内容:")
        print("1. ✅ 修复了 StockIn 模型中的 stock_in_date 字段类型")
        print("2. ✅ 修复了 StockOut 模型中的 stock_out_date 字段类型")
        print("3. ✅ 使用原始SQL避免ORM的datetime精度问题")
        print("4. ✅ 修复了存储位置查询的datetime字段问题")
        return True
    else:
        print("⚠️ 部分测试失败，可能仍存在问题")
        print("\n💡 如果仍有问题，请检查:")
        print("1. 数据库连接是否正常")
        print("2. 模型定义是否正确")
        print("3. SQL语句是否有语法错误")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
