#!/usr/bin/env python3
"""
测试供应商产品创建页面的权限过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, SupplierSchoolRelation, AdministrativeArea, User

def test_supplier_product_permission():
    """测试供应商产品创建页面的权限过滤功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 供应商产品权限过滤测试 ===\n")
        
        # 1. 查找测试数据
        print("1. 查找测试数据...")
        try:
            # 查找所有活跃供应商
            all_suppliers = Supplier.query.filter_by(status=1).all()
            print(f"   ✅ 系统中总共有 {len(all_suppliers)} 个活跃供应商")
            
            # 查找有学校关系的供应商
            suppliers_with_relations = Supplier.query.join(SupplierSchoolRelation)\
                .filter(Supplier.status == 1)\
                .filter(SupplierSchoolRelation.status == 1)\
                .distinct().all()
            print(f"   ✅ 其中有 {len(suppliers_with_relations)} 个供应商有有效的学校关系")
            
            # 查找用户
            test_user = User.query.first()
            if not test_user:
                print("   ❌ 没有找到测试用户")
                return False
            
            print(f"   ✅ 测试用户: {test_user.username}")
            print(f"   ✅ 是否为管理员: {test_user.is_admin()}")
            
        except Exception as e:
            print(f"   ❌ 查找测试数据失败: {e}")
            return False
        
        # 2. 测试管理员权限
        print("\n2. 测试管理员权限...")
        try:
            # 模拟管理员权限逻辑
            if test_user.is_admin():
                # 系统管理员可以看到所有供应商
                admin_suppliers = Supplier.query.filter_by(status=1).all()
                print("   ✅ 管理员模式：可以看到所有供应商")
            else:
                # 普通用户只能看到与自己管辖学校有合作关系的供应商
                accessible_area_ids = [area.id for area in test_user.get_accessible_areas()]
                
                # 通过供应商-学校关联表筛选供应商
                admin_suppliers = Supplier.query.join(SupplierSchoolRelation)\
                            .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                            .filter(Supplier.status == 1)\
                            .filter(SupplierSchoolRelation.status == 1)\
                            .distinct().all()
                print("   ✅ 普通用户模式：只能看到有关系的供应商")
            
            print(f"   ✅ 管理员可见供应商数量: {len(admin_suppliers)}")
            
            # 显示前5个供应商
            for supplier in admin_suppliers[:5]:
                print(f"   - 供应商: {supplier.name} (ID: {supplier.id})")
            
        except Exception as e:
            print(f"   ❌ 管理员权限测试失败: {e}")
            return False
        
        # 3. 测试普通用户权限
        print("\n3. 测试普通用户权限...")
        try:
            # 获取用户可访问的区域
            accessible_areas = test_user.get_accessible_areas()
            accessible_area_ids = [area.id for area in accessible_areas]
            print(f"   ✅ 用户可访问的区域数量: {len(accessible_areas)}")
            
            # 显示前5个可访问区域
            for area in accessible_areas[:5]:
                print(f"   - 区域: {area.name} (ID: {area.id}, Level: {area.level})")
            
            # 模拟普通用户权限逻辑
            user_suppliers = Supplier.query.join(SupplierSchoolRelation)\
                        .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                        .filter(Supplier.status == 1)\
                        .filter(SupplierSchoolRelation.status == 1)\
                        .distinct().all()
            
            print(f"   ✅ 普通用户可见供应商数量: {len(user_suppliers)}")
            
            # 显示可见的供应商及其关系
            for supplier in user_suppliers[:3]:
                print(f"   - 供应商: {supplier.name}")
                # 查找该供应商在用户区域内的关系
                relations = SupplierSchoolRelation.query.filter(
                    SupplierSchoolRelation.supplier_id == supplier.id,
                    SupplierSchoolRelation.area_id.in_(accessible_area_ids),
                    SupplierSchoolRelation.status == 1
                ).all()
                for relation in relations:
                    print(f"     与学校: {relation.area.name}")
            
        except Exception as e:
            print(f"   ❌ 普通用户权限测试失败: {e}")
            return False
        
        # 4. 验证权限过滤的正确性
        print("\n4. 验证权限过滤的正确性...")
        try:
            total_suppliers = len(all_suppliers)
            admin_visible = len(admin_suppliers)
            user_visible = len(user_suppliers)
            
            print(f"   ✅ 系统总供应商数: {total_suppliers}")
            print(f"   ✅ 管理员可见数: {admin_visible}")
            print(f"   ✅ 普通用户可见数: {user_visible}")
            
            if test_user.is_admin():
                # 管理员应该能看到所有供应商
                if admin_visible == total_suppliers:
                    print("   ✅ 管理员权限验证正确：可以看到所有供应商")
                else:
                    print("   ❌ 管理员权限验证失败：应该能看到所有供应商")
                    return False
            else:
                # 普通用户应该只能看到有关系的供应商
                if user_visible <= total_suppliers:
                    print("   ✅ 普通用户权限验证正确：只能看到部分供应商")
                else:
                    print("   ❌ 普通用户权限验证失败：看到的供应商数超过总数")
                    return False
            
            # 验证普通用户看到的供应商都有合法的关系
            for supplier in user_suppliers:
                has_valid_relation = SupplierSchoolRelation.query.filter(
                    SupplierSchoolRelation.supplier_id == supplier.id,
                    SupplierSchoolRelation.area_id.in_(accessible_area_ids),
                    SupplierSchoolRelation.status == 1
                ).first()
                
                if not has_valid_relation:
                    print(f"   ❌ 权限验证失败：供应商 {supplier.name} 在用户区域内没有有效关系")
                    return False
            
            print("   ✅ 所有可见的供应商都在用户权限范围内")
            
        except Exception as e:
            print(f"   ❌ 权限验证失败: {e}")
            return False
        
        # 5. 测试表单选项构建
        print("\n5. 测试表单选项构建...")
        try:
            # 模拟表单选项构建
            if test_user.is_admin():
                form_suppliers = Supplier.query.filter_by(status=1).all()
            else:
                form_suppliers = Supplier.query.join(SupplierSchoolRelation)\
                            .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                            .filter(Supplier.status == 1)\
                            .filter(SupplierSchoolRelation.status == 1)\
                            .distinct().all()
            
            choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in form_suppliers]
            
            print(f"   ✅ 表单选项数量: {len(choices)} (包含默认选项)")
            print(f"   ✅ 供应商选项数量: {len(form_suppliers)}")
            
            # 显示前5个选项
            for choice in choices[:6]:
                print(f"   - 选项: {choice[1]} (值: {choice[0]})")
            
        except Exception as e:
            print(f"   ❌ 表单选项构建测试失败: {e}")
            return False
        
        # 6. 对比不同权限级别的差异
        print("\n6. 对比不同权限级别的差异...")
        try:
            # 计算权限差异
            if test_user.is_admin():
                restricted_count = total_suppliers - user_visible
                print(f"   ✅ 如果是普通用户，将被限制访问 {restricted_count} 个供应商")
                print(f"   ✅ 权限过滤比例: {(user_visible/total_suppliers)*100:.1f}% 可见")
            else:
                print(f"   ✅ 当前用户权限过滤比例: {(user_visible/total_suppliers)*100:.1f}% 可见")
            
            print(f"   ✅ 有学校关系的供应商: {len(suppliers_with_relations)}")
            print(f"   ✅ 用户可见的供应商: {user_visible}")
            
        except Exception as e:
            print(f"   ❌ 权限差异分析失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！供应商产品权限过滤功能正常工作。")
        print(f"✅ 测试用户: {test_user.username}")
        print(f"✅ 用户类型: {'管理员' if test_user.is_admin() else '普通用户'}")
        print(f"✅ 可见供应商数: {len(form_suppliers)}/{total_suppliers}")
        print(f"✅ 权限过滤正确性: 验证通过")
        return True

if __name__ == '__main__':
    success = test_supplier_product_permission()
    sys.exit(0 if success else 1)
