#!/usr/bin/env python3
"""
测试轮播图修复功能
验证默认链接和CSRF令牌问题的修复
"""

import requests
from bs4 import BeautifulSoup
import re

def test_batch_upload_page():
    """测试批量上传页面的修复"""
    print("🧪 测试批量上传页面修复...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问批量上传页面
        print("📄 访问批量上传页面...")
        response = requests.get(f"{base_url}/admin/carousel/batch-upload", timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            print("✅ 页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查默认链接值
            link_input = soup.find('input', {'id': 'default_link_url'})
            if link_input:
                default_value = link_input.get('value', '')
                if default_value == '/':
                    print("✅ 默认链接值正确设置为 '/'")
                else:
                    print(f"❌ 默认链接值错误: '{default_value}'")
                    return False
            else:
                print("❌ 未找到默认链接输入框")
                return False
            
            # 检查CSRF令牌
            csrf_input = soup.find('input', {'name': 'csrf_token'})
            if csrf_input:
                csrf_value = csrf_input.get('value', '')
                if csrf_value and len(csrf_value) > 10:
                    print("✅ CSRF令牌正确设置")
                    print(f"   令牌长度: {len(csrf_value)} 字符")
                    
                    # 检查是否是隐藏字段
                    if csrf_input.get('type') == 'hidden':
                        print("✅ CSRF令牌正确设置为隐藏字段")
                    else:
                        print("⚠️  CSRF令牌不是隐藏字段")
                else:
                    print(f"❌ CSRF令牌值异常: '{csrf_value}'")
                    return False
            else:
                print("❌ 未找到CSRF令牌字段")
                return False
            
            # 检查表单设置
            form = soup.find('form', {'id': 'batchUploadForm'})
            if form:
                method = form.get('method', '').upper()
                enctype = form.get('enctype', '')
                
                if method == 'POST':
                    print("✅ 表单方法正确设置为POST")
                else:
                    print(f"❌ 表单方法错误: {method}")
                
                if enctype == 'multipart/form-data':
                    print("✅ 表单编码正确设置为multipart/form-data")
                else:
                    print(f"❌ 表单编码错误: {enctype}")
            
            # 检查快速设置按钮
            quick_buttons = soup.find_all('button', string=re.compile(r'首页|登录|注册'))
            if len(quick_buttons) >= 3:
                print(f"✅ 找到 {len(quick_buttons)} 个快速设置按钮")
            else:
                print(f"⚠️  快速设置按钮数量不足: {len(quick_buttons)}")
            
            return True
            
        elif response.status_code in [302, 301]:
            print("⚠️  页面重定向到登录页面（需要登录）")
            return True  # 重定向是正常的
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_single_upload_page():
    """测试单张上传页面的修复"""
    print("\n🧪 测试单张上传页面修复...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问单张上传页面
        print("📄 访问单张上传页面...")
        response = requests.get(f"{base_url}/admin/carousel/create", timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            print("✅ 页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查默认链接值
            link_input = soup.find('input', {'id': 'link_url'})
            if link_input:
                default_value = link_input.get('value', '')
                if default_value == '/':
                    print("✅ 默认链接值正确设置为 '/'")
                else:
                    print(f"❌ 默认链接值错误: '{default_value}'")
                    return False
            else:
                print("❌ 未找到链接输入框")
                return False
            
            # 检查CSRF令牌
            csrf_input = soup.find('input', {'name': 'csrf_token'})
            if csrf_input:
                csrf_value = csrf_input.get('value', '')
                if csrf_value and len(csrf_value) > 10:
                    print("✅ CSRF令牌正确设置")
                    
                    # 检查是否是隐藏字段
                    if csrf_input.get('type') == 'hidden':
                        print("✅ CSRF令牌正确设置为隐藏字段")
                    else:
                        print("⚠️  CSRF令牌不是隐藏字段")
                else:
                    print(f"❌ CSRF令牌值异常: '{csrf_value}'")
                    return False
            else:
                print("❌ 未找到CSRF令牌字段")
                return False
            
            # 检查多图片上传支持
            file_input = soup.find('input', {'id': 'images'})
            if file_input:
                if file_input.get('multiple') is not None:
                    print("✅ 支持多图片上传")
                else:
                    print("❌ 不支持多图片上传")
                    return False
            else:
                print("❌ 未找到文件上传字段")
                return False
            
            return True
            
        elif response.status_code in [302, 301]:
            print("⚠️  页面重定向到登录页面（需要登录）")
            return True  # 重定向是正常的
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_carousel_list_page():
    """测试轮播图列表页面"""
    print("\n🧪 测试轮播图列表页面...")
    print("-" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        # 访问列表页面
        print("📄 访问轮播图列表页面...")
        response = requests.get(f"{base_url}/admin/carousel", timeout=10, allow_redirects=True)
        
        if response.status_code == 200:
            print("✅ 页面访问成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查批量上传按钮
            batch_upload_link = soup.find('a', href=re.compile(r'/admin/carousel/batch-upload'))
            if batch_upload_link:
                print("✅ 找到批量上传按钮")
                
                # 检查按钮文本
                if '批量上传' in batch_upload_link.get_text():
                    print("✅ 批量上传按钮文本正确")
                else:
                    print("⚠️  批量上传按钮文本可能不正确")
            else:
                print("❌ 未找到批量上传按钮")
                return False
            
            # 检查添加轮播图按钮
            add_link = soup.find('a', href=re.compile(r'/admin/carousel/create'))
            if add_link:
                print("✅ 找到添加轮播图按钮")
            else:
                print("❌ 未找到添加轮播图按钮")
                return False
            
            return True
            
        elif response.status_code in [302, 301]:
            print("⚠️  页面重定向到登录页面（需要登录）")
            return True  # 重定向是正常的
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 轮播图修复功能测试")
    print("=" * 60)
    print("测试默认链接和CSRF令牌修复")
    print("=" * 60)
    
    # 执行所有测试
    tests = [
        ("轮播图列表页面", test_carousel_list_page()),
        ("单张上传页面", test_single_upload_page()),
        ("批量上传页面", test_batch_upload_page()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 修复测试结果")
    print("=" * 60)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有修复测试通过！")
        print("\n✅ 修复内容确认:")
        print("1. ✅ 默认链接值正确设置为 '/'")
        print("2. ✅ CSRF令牌正确设置为隐藏字段")
        print("3. ✅ 多图片上传功能正常")
        print("4. ✅ 批量上传按钮正确显示")
        print("5. ✅ 表单配置正确")
        
        print("\n📋 现在可以正常使用:")
        print("1. 访问: http://127.0.0.1:5000/admin/carousel")
        print("2. 点击'批量上传'按钮")
        print("3. 默认链接已设置为 '/' (首页)")
        print("4. CSRF令牌已正确隐藏")
        
    else:
        print("\n⚠️  部分修复测试失败")
        print("\n🔧 建议操作:")
        print("1. 检查应用是否正常运行")
        print("2. 清除浏览器缓存")
        print("3. 重启应用服务器")
        print("4. 再次运行此测试")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    main()
