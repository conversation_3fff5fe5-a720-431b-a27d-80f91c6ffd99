#!/usr/bin/env python3
"""
测试在线咨询路由
验证所有路由是否正确注册
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_routes():
    """测试路由注册"""
    try:
        from app import create_app
        
        app = create_app()
        
        print("🔍 检查在线咨询相关路由...")
        print("=" * 60)
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # 查找咨询相关路由
        consultation_routes = [r for r in routes if 'consultation' in r['endpoint'] or 'consultation' in r['rule']]
        
        if consultation_routes:
            print("✅ 找到以下咨询相关路由：")
            print("-" * 60)
            for route in consultation_routes:
                methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
                print(f"端点: {route['endpoint']}")
                print(f"路径: {route['rule']}")
                print(f"方法: {', '.join(methods)}")
                print("-" * 60)
        else:
            print("❌ 未找到咨询相关路由！")
            return False
        
        # 检查关键路由
        key_routes = [
            '/consultation/api/submit',
            '/consultation/list',
            '/consultation/<int:id>',
            '/consultation/<int:id>/reply',
            '/consultation/<int:id>/close'
        ]
        
        print("\n🎯 检查关键路由：")
        print("-" * 60)
        
        found_routes = [r['rule'] for r in consultation_routes]
        
        for key_route in key_routes:
            if key_route in found_routes:
                print(f"✅ {key_route}")
            else:
                print(f"❌ {key_route} - 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 路由测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """测试API端点"""
    import requests
    
    print("\n🌐 测试API端点...")
    print("-" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试数据
    test_data = {
        "name": "测试用户",
        "contact_type": "微信",
        "contact_value": "test_user_123",
        "content": "这是一个路由测试，验证API是否正常工作。"
    }
    
    try:
        # 测试API端点
        response = requests.post(
            f"{base_url}/consultation/api/submit",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        
        print(f"API状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ API测试成功！")
                print(f"响应: {result.get('message')}")
                return True
            else:
                print(f"❌ API返回错误: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 应用可能未运行")
        print("请先启动应用: python app.py")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 在线咨询路由测试")
    print("=" * 60)
    
    # 测试路由注册
    routes_ok = test_routes()
    
    if not routes_ok:
        print("\n❌ 路由测试失败！")
        return
    
    print("\n" + "=" * 60)
    print("是否要测试API端点？(需要应用正在运行)")
    choice = input("输入 y 继续，其他键跳过: ").lower().strip()
    
    if choice == 'y':
        api_ok = test_api_endpoint()
        
        if api_ok:
            print("\n✅ 所有测试通过！")
            print("\n📋 使用说明：")
            print("1. 访问首页: http://127.0.0.1:5000")
            print("2. 滚动到底部填写咨询表单")
            print("3. 管理员访问: http://127.0.0.1:5000/consultation/list")
        else:
            print("\n❌ API测试失败！")
    else:
        print("\n✅ 路由注册测试通过！")
        print("\n📋 下一步：")
        print("1. 启动应用: python app.py")
        print("2. 运行完整测试: python test_consultation.py")

if __name__ == '__main__':
    main()
