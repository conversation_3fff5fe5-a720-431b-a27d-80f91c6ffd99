#!/usr/bin/env python3
"""
修复在线咨询数据库问题
解决时间精度和字段问题
"""

from app import create_app, db
from sqlalchemy import text

def check_table_structure():
    """检查表结构"""
    print("🔍 检查在线咨询表结构...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查表是否存在
            check_table_sql = text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'online_consultations'
            """)
            result = db.session.execute(check_table_sql).fetchone()
            
            if result.count == 0:
                print("❌ online_consultations 表不存在")
                return False
            
            print("✅ online_consultations 表存在")
            
            # 检查表结构
            structure_sql = text("""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'online_consultations'
                ORDER BY ORDINAL_POSITION
            """)
            
            columns = db.session.execute(structure_sql).fetchall()
            
            print("\n📋 当前表结构:")
            for col in columns:
                nullable = "NULL" if col.IS_NULLABLE == "YES" else "NOT NULL"
                default = f" DEFAULT {col.COLUMN_DEFAULT}" if col.COLUMN_DEFAULT else ""
                print(f"  - {col.COLUMN_NAME}: {col.DATA_TYPE} {nullable}{default}")
            
            # 检查必要字段
            column_names = [col.COLUMN_NAME for col in columns]
            required_fields = ['id', 'name', 'contact_type', 'contact_value', 'content', 'status', 'source', 'created_at', 'updated_at']
            
            missing_fields = []
            for field in required_fields:
                if field not in column_names:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"\n⚠️  缺少字段: {', '.join(missing_fields)}")
                return False
            else:
                print("\n✅ 所有必要字段都存在")
                return True
                
        except Exception as e:
            print(f"❌ 检查表结构失败: {str(e)}")
            return False

def fix_table_structure():
    """修复表结构"""
    print("\n🔧 修复表结构...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查并添加缺少的字段
            structure_sql = text("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'online_consultations'
            """)
            
            columns = db.session.execute(structure_sql).fetchall()
            column_names = [col.COLUMN_NAME for col in columns]
            
            # 需要添加的字段
            fields_to_add = []
            
            if 'status' not in column_names:
                fields_to_add.append(("status", "NVARCHAR(20) NOT NULL DEFAULT '待处理'"))
            
            if 'source' not in column_names:
                fields_to_add.append(("source", "NVARCHAR(50) NULL DEFAULT '官网首页'"))
            
            if 'reply_content' not in column_names:
                fields_to_add.append(("reply_content", "NVARCHAR(MAX) NULL"))
            
            if 'reply_time' not in column_names:
                fields_to_add.append(("reply_time", "DATETIME2(1) NULL"))
            
            if 'reply_user_id' not in column_names:
                fields_to_add.append(("reply_user_id", "INT NULL"))
            
            # 添加缺少的字段
            for field_name, field_definition in fields_to_add:
                try:
                    alter_sql = text(f"ALTER TABLE online_consultations ADD {field_name} {field_definition}")
                    db.session.execute(alter_sql)
                    db.session.commit()
                    print(f"✅ 添加字段: {field_name}")
                except Exception as e:
                    print(f"⚠️  添加字段 {field_name} 失败: {str(e)}")
            
            # 检查时间字段精度
            time_fields_sql = text("""
                SELECT COLUMN_NAME, DATA_TYPE, DATETIME_PRECISION
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'online_consultations'
                AND DATA_TYPE = 'datetime2'
            """)
            
            time_fields = db.session.execute(time_fields_sql).fetchall()
            
            for field in time_fields:
                if field.DATETIME_PRECISION != 1:
                    try:
                        # 修改时间字段精度
                        alter_time_sql = text(f"""
                            ALTER TABLE online_consultations 
                            ALTER COLUMN {field.COLUMN_NAME} DATETIME2(1)
                        """)
                        db.session.execute(alter_time_sql)
                        db.session.commit()
                        print(f"✅ 修复时间字段精度: {field.COLUMN_NAME}")
                    except Exception as e:
                        print(f"⚠️  修复时间字段 {field.COLUMN_NAME} 精度失败: {str(e)}")
            
            print("\n✅ 表结构修复完成")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 表结构修复失败: {str(e)}")
            return False

def clean_invalid_data():
    """清理无效数据"""
    print("\n🧹 清理无效数据...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 查找包含CSRF令牌的记录
            csrf_sql = text("""
                SELECT id, content 
                FROM online_consultations 
                WHERE content LIKE '%IjQzNTBlYjg3NjNlMDNkMGM3MTNiYWE2NmQ4ZjY1NzBlZjQ1NzFmZTMi%'
                OR content LIKE '%.%'
                AND LEN(content) > 100
            """)
            
            invalid_records = db.session.execute(csrf_sql).fetchall()
            
            if invalid_records:
                print(f"🗑️  找到 {len(invalid_records)} 条包含CSRF令牌的无效记录")
                
                choice = input("是否删除这些无效记录？(y/N): ").strip().lower()
                if choice == 'y':
                    for record in invalid_records:
                        delete_sql = text("DELETE FROM online_consultations WHERE id = :id")
                        db.session.execute(delete_sql, {'id': record.id})
                        print(f"  删除记录 ID: {record.id}")
                    
                    db.session.commit()
                    print(f"✅ 已删除 {len(invalid_records)} 条无效记录")
                else:
                    print("⏭️  跳过删除无效记录")
            else:
                print("✅ 没有发现无效记录")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 清理无效数据失败: {str(e)}")
            return False

def test_insert():
    """测试插入功能"""
    print("\n🧪 测试插入功能...")
    print("-" * 50)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 测试插入
            insert_sql = text("""
                INSERT INTO online_consultations
                (name, contact_type, contact_value, content, status, source, ip_address, user_agent)
                OUTPUT inserted.id
                VALUES
                (:name, :contact_type, :contact_value, :content, :status, :source, :ip_address, :user_agent)
            """)
            
            result = db.session.execute(insert_sql, {
                'name': '测试用户',
                'contact_type': '电话',
                'contact_value': '18373062333',
                'content': '这是一个数据库修复测试，验证插入功能是否正常。',
                'status': '待处理',
                'source': '修复测试',
                'ip_address': '127.0.0.1',
                'user_agent': 'Test Agent'
            })
            
            consultation_id = result.fetchone()[0]
            db.session.commit()
            
            print(f"✅ 测试插入成功，ID: {consultation_id}")
            
            # 删除测试记录
            delete_sql = text("DELETE FROM online_consultations WHERE id = :id")
            db.session.execute(delete_sql, {'id': consultation_id})
            db.session.commit()
            
            print("✅ 测试记录已清理")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 测试插入失败: {str(e)}")
            return False

def main():
    """主修复函数"""
    print("🔧 在线咨询数据库修复工具")
    print("=" * 60)
    print("修复时间精度和字段问题")
    print("=" * 60)
    
    # 执行修复步骤
    steps = [
        ("检查表结构", check_table_structure()),
        ("修复表结构", fix_table_structure()),
        ("清理无效数据", clean_invalid_data()),
        ("测试插入功能", test_insert()),
    ]
    
    print("\n" + "=" * 60)
    print("📊 修复结果")
    print("=" * 60)
    
    for step_name, result in steps:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{step_name:<15}: {status}")
    
    passed_steps = sum(1 for _, result in steps if result)
    total_steps = len(steps)
    
    print(f"\n📊 总体结果: {passed_steps}/{total_steps} 步骤成功")
    
    if passed_steps == total_steps:
        print("\n🎉 数据库修复完成！")
        print("\n✅ 修复内容:")
        print("1. ✅ 表结构已完善")
        print("2. ✅ 时间字段精度已修正")
        print("3. ✅ 无效数据已清理")
        print("4. ✅ 插入功能已验证")
        
        print("\n📋 现在可以:")
        print("1. 重启应用服务器")
        print("2. 测试在线咨询功能")
        print("3. 运行: python test_consultation_fixes.py")
        
    else:
        print("\n⚠️  部分修复失败")
        print("\n🔧 建议操作:")
        print("1. 检查数据库连接")
        print("2. 确认数据库权限")
        print("3. 手动执行SQL语句")
        print("4. 联系数据库管理员")
    
    return passed_steps == total_steps

if __name__ == '__main__':
    main()
