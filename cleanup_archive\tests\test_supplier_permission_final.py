#!/usr/bin/env python3
"""
最终测试：供应商权限过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Supplier, SupplierSchoolRelation, AdministrativeArea, User

def test_supplier_permission_final():
    """最终测试：供应商权限过滤功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 供应商权限过滤功能最终测试 ===\n")
        
        # 1. 查找测试数据
        print("1. 查找测试数据...")
        try:
            # 查找有学校关系的供应商
            suppliers_with_relations = Supplier.query.join(SupplierSchoolRelation).filter(
                SupplierSchoolRelation.status == 1
            ).distinct().all()
            
            if not suppliers_with_relations:
                print("   ❌ 没有找到有学校关系的供应商")
                return False
            
            print(f"   ✅ 找到 {len(suppliers_with_relations)} 个有学校关系的供应商")
            
            # 选择第一个供应商进行测试
            test_supplier = suppliers_with_relations[0]
            print(f"   ✅ 测试供应商: {test_supplier.name} (ID: {test_supplier.id})")
            
            # 显示该供应商的所有学校关系
            all_relations = test_supplier.school_relations
            print(f"   ✅ 该供应商总共有 {len(all_relations)} 个学校关系")
            
            for relation in all_relations:
                print(f"   - 学校: {relation.area.name} (ID: {relation.area_id})")
                print(f"     状态: {'有效' if relation.status == 1 else '已终止'}")
            
        except Exception as e:
            print(f"   ❌ 查找测试数据失败: {e}")
            return False
        
        # 2. 测试用户权限
        print("\n2. 测试用户权限...")
        try:
            # 查找用户
            test_user = User.query.first()
            if not test_user:
                print("   ❌ 没有找到用户")
                return False
            
            print(f"   ✅ 测试用户: {test_user.username}")
            print(f"   ✅ 是否为管理员: {test_user.is_admin()}")
            
            # 获取用户可访问的区域
            accessible_areas = test_user.get_accessible_areas()
            accessible_area_ids = [area.id for area in accessible_areas]
            print(f"   ✅ 用户可访问的区域数量: {len(accessible_areas)}")
            
            # 显示前5个可访问区域
            for area in accessible_areas[:5]:
                print(f"   - 区域: {area.name} (ID: {area.id}, Level: {area.level})")
            
        except Exception as e:
            print(f"   ❌ 用户权限测试失败: {e}")
            return False
        
        # 3. 测试权限过滤逻辑
        print("\n3. 测试权限过滤逻辑...")
        try:
            # 模拟列表页面的权限过滤逻辑
            if test_user.is_admin():
                # 管理员可以看到所有学校关系
                accessible_relations = test_supplier.school_relations
                print("   ✅ 管理员模式：可以看到所有学校关系")
            else:
                # 普通用户只能看到自己区域内的学校关系
                accessible_relations = [
                    relation for relation in test_supplier.school_relations 
                    if relation.area_id in accessible_area_ids
                ]
                print("   ✅ 普通用户模式：只能看到自己区域内的学校关系")
            
            print(f"   ✅ 过滤后可见的学校关系数量: {len(accessible_relations)}")
            
            for relation in accessible_relations:
                print(f"   - 可见学校: {relation.area.name}")
                print(f"     合同编号: {relation.contract_number}")
                print(f"     状态: {'有效' if relation.status == 1 else '已终止'}")
            
        except Exception as e:
            print(f"   ❌ 权限过滤逻辑测试失败: {e}")
            return False
        
        # 4. 验证权限过滤的正确性
        print("\n4. 验证权限过滤的正确性...")
        try:
            total_relations = len(test_supplier.school_relations)
            filtered_relations = len(accessible_relations)
            
            print(f"   ✅ 总学校关系数: {total_relations}")
            print(f"   ✅ 过滤后关系数: {filtered_relations}")
            
            if test_user.is_admin():
                # 管理员应该能看到所有关系
                if filtered_relations == total_relations:
                    print("   ✅ 管理员权限验证正确：可以看到所有学校关系")
                else:
                    print("   ❌ 管理员权限验证失败：应该能看到所有学校关系")
                    return False
            else:
                # 普通用户应该只能看到自己区域的关系
                if filtered_relations <= total_relations:
                    print("   ✅ 普通用户权限验证正确：只能看到部分学校关系")
                else:
                    print("   ❌ 普通用户权限验证失败：看到的关系数超过总数")
                    return False
                
                # 验证所有可见的关系都在用户的权限范围内
                for relation in accessible_relations:
                    if relation.area_id not in accessible_area_ids:
                        print(f"   ❌ 权限验证失败：用户看到了不属于自己区域的学校 {relation.area.name}")
                        return False
                
                print("   ✅ 所有可见的学校关系都在用户权限范围内")
            
        except Exception as e:
            print(f"   ❌ 权限验证失败: {e}")
            return False
        
        # 5. 测试多个供应商的权限过滤
        print("\n5. 测试多个供应商的权限过滤...")
        try:
            # 模拟列表页面为多个供应商添加权限过滤
            test_suppliers = suppliers_with_relations[:3]  # 测试前3个供应商
            
            if test_user.is_admin():
                # 管理员可以看到所有学校关系
                for supplier in test_suppliers:
                    supplier.accessible_relations = supplier.school_relations
            else:
                # 普通用户只能看到自己区域内的学校关系
                for supplier in test_suppliers:
                    supplier.accessible_relations = [
                        relation for relation in supplier.school_relations 
                        if relation.area_id in accessible_area_ids
                    ]
            
            print(f"   ✅ 为 {len(test_suppliers)} 个供应商添加了权限过滤")
            
            for supplier in test_suppliers:
                total = len(supplier.school_relations)
                accessible = len(supplier.accessible_relations)
                print(f"   - {supplier.name}: {accessible}/{total} 个可见学校关系")
            
        except Exception as e:
            print(f"   ❌ 多供应商权限过滤测试失败: {e}")
            return False
        
        print("\n=== 测试完成 ===")
        print("✅ 所有测试通过！供应商权限过滤功能正常工作。")
        print(f"✅ 测试用户: {test_user.username}")
        print(f"✅ 用户类型: {'管理员' if test_user.is_admin() else '普通用户'}")
        print(f"✅ 可访问区域数: {len(accessible_areas)}")
        print(f"✅ 权限过滤正确性: 验证通过")
        return True

if __name__ == '__main__':
    success = test_supplier_permission_final()
    sys.exit(0 if success else 1)
