"""
修复所有表中的DATETIME字段

此脚本直接连接到SQL Server数据库，扫描所有表中的datetime字段，
并将它们修改为DATETIME2(1)类型，解决精度问题。
"""
import pyodbc
import sys
import logging
from datetime import datetime

# 配置信息
SQL_SERVER = 'localhost\\SQLEXPRESS'
SQL_SERVER_DB = 'StudentsCMSSP'

# 配置日志
log_file = f"fix_all_datetime_fields_{datetime.now().strftime('%Y%m%d%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_sqlserver_connection():
    """获取SQL Server数据库连接"""
    try:
        conn_str = f'DRIVER={{SQL Server}};SERVER={SQL_SERVER};DATABASE={SQL_SERVER_DB};Trusted_Connection=yes;'
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        logger.error(f"错误: 无法连接到SQL Server: {e}")
        return None

def get_all_tables(conn):
    """获取数据库中的所有表"""
    cursor = conn.cursor()
    try:
        cursor.execute("""
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """)
        tables = [row[0] for row in cursor.fetchall()]
        return tables
    except pyodbc.Error as e:
        logger.error(f"获取表列表时出错: {e}")
        return []
    finally:
        cursor.close()

def get_datetime_columns(conn, table_name):
    """获取表中的所有datetime类型字段"""
    cursor = conn.cursor()
    try:
        cursor.execute(f"""
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
        AND (DATA_TYPE LIKE '%datetime%' OR DATA_TYPE = 'date')
        ORDER BY ORDINAL_POSITION
        """)
        columns = [(row[0], row[1], row[2]) for row in cursor.fetchall()]
        return columns
    except pyodbc.Error as e:
        logger.error(f"获取表 {table_name} 的字段时出错: {e}")
        return []
    finally:
        cursor.close()

def drop_default_constraint(conn, table_name, column_name):
    """删除表中列的默认约束"""
    cursor = conn.cursor()
    try:
        # 查找默认约束名称
        cursor.execute(f"""
        SELECT dc.name
        FROM sys.default_constraints dc
        JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
        WHERE OBJECT_NAME(dc.parent_object_id) = '{table_name}' AND c.name = '{column_name}'
        """)
        
        constraint = cursor.fetchone()
        if constraint:
            constraint_name = constraint[0]
            cursor.execute(f"ALTER TABLE {table_name} DROP CONSTRAINT {constraint_name}")
            conn.commit()
            logger.info(f"已删除 {table_name} 表的 {column_name} 列的默认约束: {constraint_name}")
            return True
        else:
            logger.info(f"{table_name} 表的 {column_name} 列没有默认约束")
            return False
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"删除默认约束时出错: {e}")
        return False
    finally:
        cursor.close()

def add_default_constraint(conn, table_name, column_name, default_value="GETDATE()"):
    """为表中的列添加默认约束"""
    cursor = conn.cursor()
    try:
        constraint_name = f"DF_{table_name}_{column_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        cursor.execute(f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} DEFAULT ({default_value}) FOR {column_name}")
        conn.commit()
        logger.info(f"已为 {table_name} 表的 {column_name} 列添加默认约束: {constraint_name}")
        return True
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"添加默认约束时出错: {e}")
        return False
    finally:
        cursor.close()

def alter_column_type(conn, table_name, column_name, data_type, is_nullable):
    """修改列的数据类型"""
    cursor = conn.cursor()
    try:
        nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
        
        # 对于datetime类型，修改为DATETIME2(1)
        if 'datetime' in data_type.lower() and 'datetime2' not in data_type.lower():
            new_type = "DATETIME2(1)"
            cursor.execute(f"ALTER TABLE {table_name} ALTER COLUMN {column_name} {new_type} {nullable}")
            conn.commit()
            logger.info(f"已修改 {table_name} 表的 {column_name} 列为 {new_type} {nullable}")
            return True
        else:
            logger.info(f"跳过 {table_name} 表的 {column_name} 列，类型为 {data_type}")
            return False
    except pyodbc.Error as e:
        conn.rollback()
        logger.error(f"修改列类型时出错: {e}")
        return False
    finally:
        cursor.close()

def fix_table_datetime_columns(conn, table_name):
    """修复表中的所有datetime字段"""
    logger.info(f"开始修复表 {table_name} 的datetime字段...")
    
    # 获取表中的datetime字段
    columns = get_datetime_columns(conn, table_name)
    if not columns:
        logger.info(f"表 {table_name} 中没有datetime字段，跳过")
        return 0
    
    fixed_count = 0
    for column_name, data_type, is_nullable in columns:
        # 只处理datetime类型，不处理date类型
        if 'datetime' not in data_type.lower():
            continue
        
        try:
            # 1. 删除默认约束（如果有）
            has_default = drop_default_constraint(conn, table_name, column_name)
            
            # 2. 修改列类型
            if alter_column_type(conn, table_name, column_name, data_type, is_nullable):
                fixed_count += 1
            
            # 3. 重新添加默认约束（如果之前有）
            if has_default and 'created_at' in column_name.lower() or 'updated_at' in column_name.lower():
                add_default_constraint(conn, table_name, column_name)
        except Exception as e:
            logger.error(f"修复 {table_name} 表的 {column_name} 列时出错: {e}")
    
    logger.info(f"表 {table_name} 修复完成，共修复 {fixed_count} 个字段")
    return fixed_count

def fix_all_tables(conn):
    """修复所有表中的datetime字段"""
    tables = get_all_tables(conn)
    logger.info(f"找到 {len(tables)} 个表")
    
    total_fixed = 0
    for table_name in tables:
        fixed_count = fix_table_datetime_columns(conn, table_name)
        total_fixed += fixed_count
    
    logger.info(f"所有表修复完成，共修复 {total_fixed} 个字段")
    return total_fixed

def main():
    """主函数"""
    print("\n" + "="*80)
    print(" "*20 + "修复所有表中的DATETIME字段")
    print("="*80)
    print(f"\n日志文件: {log_file}")
    
    # 获取数据库连接
    conn = get_sqlserver_connection()
    if not conn:
        print("无法连接到数据库，程序退出")
        sys.exit(1)
    
    try:
        print("\n开始修复所有表...")
        total_fixed = fix_all_tables(conn)
        print(f"\n修复完成，共修复 {total_fixed} 个字段")
    except Exception as e:
        logger.error(f"程序执行时出错: {e}")
        print(f"\n错误: {e}")
    finally:
        conn.close()
        print("\n数据库连接已关闭")
        print("\n" + "="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.critical(f"程序发生未处理的异常: {e}")
        print(f"\n程序发生错误: {e}")
    finally:
        print("\n程序已退出")
