"""
修改应用程序中的时间格式化函数，确保它们在显示时只显示到分钟级别

此脚本将修改应用程序中的时间格式化函数和模板，
使其在显示时只显示到分钟级别。

注意：这与数据库中的时间精度设置（精度为1）相匹配，确保整个系统中的时间表示一致。
"""

import os
import re
import sys

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

def find_template_files(directory):
    """查找目录中的所有HTML模板文件"""
    template_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                template_files.append(os.path.join(root, file))
    return template_files

def fix_template_file(file_path):
    """修改模板文件中的时间格式化"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 模式1: {{ value.strftime('%Y-%m-%d %H:%M:%S') }}
        pattern1 = r'{{ *([a-zA-Z0-9_.]+)\.strftime\([\'"]%Y-%m-%d %H:%M:%S[\'"](\s*\+\s*[^}]+)?\) *}}'
        replacement1 = r'{{ \1.strftime("%Y-%m-%d %H:%M") }}'
        content = re.sub(pattern1, replacement1, content)

        # 模式2: {{ value|format_datetime('%Y-%m-%d %H:%M:%S') }}
        pattern2 = r'{{ *([a-zA-Z0-9_.]+)\|format_datetime\([\'"]%Y-%m-%d %H:%M:%S[\'"](\s*\+\s*[^}]+)?\) *}}'
        replacement2 = r'{{ \1|format_datetime("%Y-%m-%d %H:%M") }}'
        content = re.sub(pattern2, replacement2, content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改模板文件: {os.path.relpath(file_path, PROJECT_ROOT)}")
            return 1
        else:
            return 0
    except Exception as e:
        print(f"处理模板文件 {file_path} 时出错: {e}")
        return 0

def fix_python_file(file_path):
    """修改Python文件中的时间格式化"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 模式1: strftime('%Y-%m-%d %H:%M:%S')
        pattern1 = r'\.strftime\([\'"]%Y-%m-%d %H:%M:%S[\'"]\)'
        replacement1 = r'.strftime("%Y-%m-%d %H:%M")'
        content = re.sub(pattern1, replacement1, content)

        # 模式2: datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        pattern2 = r'datetime\.strptime\(([^,]+),\s*[\'"]%Y-%m-%d %H:%M:%S[\'"]\)'
        replacement2 = r'datetime.strptime(\1, "%Y-%m-%d %H:%M")'
        content = re.sub(pattern2, replacement2, content)

        # 模式3: to_dict方法中的时间格式化
        pattern3 = r'([\'"]created_at[\'"]: self\.created_at\.strftime\([\'"]%Y-%m-%d %H:%M:%S[\'"]\))'
        replacement3 = r'\1.replace(":00", "")'
        content = re.sub(pattern3, replacement3, content)

        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修改Python文件: {os.path.relpath(file_path, PROJECT_ROOT)}")
            return 1
        else:
            return 0
    except Exception as e:
        print(f"处理Python文件 {file_path} 时出错: {e}")
        return 0

def fix_format_datetime_filter(app_init_file):
    """修改format_datetime过滤器"""
    try:
        file_path = os.path.join(PROJECT_ROOT, app_init_file)
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 保存原始内容
        original_content = content

        # 查找format_datetime过滤器定义
        filter_pattern = r'@app\.template_filter\([\'"]format_datetime[\'"]\)\s*def format_datetime\(value, format=[\'"]%Y-%m-%d %H:%M:%S[\'"]\):'
        if re.search(filter_pattern, content):
            # 修改默认格式
            replacement = r'@app.template_filter("format_datetime")\ndef format_datetime(value, format="%Y-%m-%d %H:%M"):'
            content = re.sub(filter_pattern, replacement, content)

            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"已修改format_datetime过滤器: {app_init_file}")
                return 1

        return 0
    except Exception as e:
        print(f"修改format_datetime过滤器时出错: {e}")
        return 0

def main():
    """主函数"""
    print("开始修改应用程序中的时间格式化...")

    # 修改format_datetime过滤器
    fix_format_datetime_filter('app/__init__.py')

    # 修改模板文件
    template_dir = os.path.join(PROJECT_ROOT, 'app/templates')
    template_files = find_template_files(template_dir)
    print(f"找到 {len(template_files)} 个模板文件")

    template_modified_count = 0
    for file_path in template_files:
        template_modified_count += fix_template_file(file_path)

    print(f"共修改了 {template_modified_count} 个模板文件")

    # 修改Python文件
    python_dirs = ['app/routes', 'app/models', 'app/utils', 'app/api']
    python_modified_count = 0

    for dir_path in python_dirs:
        full_dir_path = os.path.join(PROJECT_ROOT, dir_path)
        if os.path.exists(full_dir_path):
            for root, dirs, files in os.walk(full_dir_path):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        python_modified_count += fix_python_file(file_path)

    print(f"共修改了 {python_modified_count} 个Python文件")
    print("\n修改完成")

if __name__ == "__main__":
    main()
