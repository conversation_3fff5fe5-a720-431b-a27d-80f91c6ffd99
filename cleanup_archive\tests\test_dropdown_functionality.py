#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
下拉菜单功能测试脚本
验证移动端和桌面端下拉菜单是否能正常展开
"""

import os
import sys

def test_html_dropdown_structure():
    """测试HTML下拉菜单结构"""
    print("🔍 测试HTML下拉菜单结构...")

    try:
        with open('app/templates/base.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 检查关键的下拉菜单结构
        dropdown_elements = [
            ('class="nav-item dropdown"', '下拉菜单容器'),
            ('class="nav-link dropdown-toggle"', '下拉菜单触发器'),
            ('data-toggle="dropdown"', 'Bootstrap下拉触发属性'),
            ('aria-haspopup="true"', 'ARIA可访问性属性'),
            ('aria-expanded="false"', 'ARIA展开状态'),
            ('class="dropdown-menu"', '下拉菜单内容'),
            ('class="dropdown-item"', '下拉菜单项'),
            ('class="dropdown-header"', '下拉菜单分组标题')
        ]

        results = []
        for element, description in dropdown_elements:
            if element in html_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)

        return all(results)

    except Exception as e:
        print(f"❌ HTML结构测试失败: {str(e)}")
        return False

def test_javascript_dropdown_fix():
    """测试JavaScript下拉菜单修复"""
    print("\n⚡ 测试JavaScript下拉菜单修复...")

    try:
        with open('app/templates/base.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 检查JavaScript修复代码
        js_features = [
            ('$(document).ready(function()', 'jQuery文档就绪'),
            ('.dropdown-toggle\').on(\'click\'', '点击事件绑定'),
            ('e.preventDefault()', '阻止默认行为'),
            ('e.stopPropagation()', '阻止事件冒泡'),
            ('toggleClass(\'show\')', '切换显示状态'),
            ('attr(\'aria-expanded\'', 'ARIA状态更新'),
            ('$(document).on(\'click\'', '外部点击监听'),
            ('window.innerWidth <= 768', '移动端检测')
        ]

        results = []
        for feature, description in js_features:
            if feature in html_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)

        return all(results)

    except Exception as e:
        print(f"❌ JavaScript测试失败: {str(e)}")
        return False

def test_css_dropdown_styles():
    """测试CSS下拉菜单样式"""
    print("\n🎨 测试CSS下拉菜单样式...")

    try:
        with open('app/static/css/elegant-navigation.css', 'r', encoding='utf-8') as f:
            css_content = f.read()

        # 检查关键的CSS样式
        css_styles = [
            ('.dropdown-menu', '下拉菜单基础样式'),
            ('.dropdown-item', '下拉菜单项样式'),
            ('.dropdown-header', '下拉菜单标题样式'),
            ('.mobile-dropdown', '移动端下拉菜单样式'),
            ('position: static !important', '移动端定位修复'),
            ('width: 100% !important', '移动端全宽'),
            ('@media (max-width: 768px)', '移动端媒体查询'),
            ('backdrop-filter', '毛玻璃效果')
        ]

        results = []
        for style, description in css_styles:
            if style in css_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)

        return all(results)

    except Exception as e:
        print(f"❌ CSS样式测试失败: {str(e)}")
        return False

def test_bootstrap_integration():
    """测试Bootstrap集成"""
    print("\n🔗 测试Bootstrap集成...")

    try:
        with open('app/templates/base.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 检查Bootstrap相关文件
        bootstrap_files = [
            ('bootstrap.bundle.min.js', 'Bootstrap JavaScript'),
            ('jquery.min.js', 'jQuery库'),
            ('bootstrap/js/bootstrap-zh-CN.js', 'Bootstrap中文化'),
            ('data-toggle="dropdown"', 'Bootstrap下拉触发器')
        ]

        results = []
        for file_ref, description in bootstrap_files:
            if file_ref in html_content:
                print(f"  ✅ {description}")
                results.append(True)
            else:
                print(f"  ❌ {description}")
                results.append(False)

        return all(results)

    except Exception as e:
        print(f"❌ Bootstrap集成测试失败: {str(e)}")
        return False

def analyze_dropdown_workflow():
    """分析下拉菜单工作流程"""
    print("\n🔄 分析下拉菜单工作流程...")

    workflow_steps = [
        "1. 用户点击导航菜单项（带有dropdown-toggle类）",
        "2. JavaScript阻止默认链接跳转行为（e.preventDefault()）",
        "3. JavaScript切换dropdown和dropdown-menu的show类",
        "4. CSS显示下拉菜单内容",
        "5. 用户可以点击子菜单项",
        "6. 点击外部区域或其他菜单项时关闭当前下拉菜单"
    ]

    print("  📋 预期工作流程:")
    for step in workflow_steps:
        print(f"    {step}")

    print("\n  🎯 关键技术点:")
    print("    • 使用href='#'防止页面跳转")
    print("    • JavaScript手动控制show类的添加/移除")
    print("    • CSS确保移动端下拉菜单正确显示")
    print("    • 外部点击监听器自动关闭菜单")

    return True

def generate_troubleshooting_guide():
    """生成故障排除指南"""
    print("\n🔧 故障排除指南:")
    print("-" * 50)

    issues_and_solutions = [
        {
            "问题": "点击菜单项页面刷新",
            "原因": "JavaScript没有阻止默认行为",
            "解决": "确保e.preventDefault()正常执行"
        },
        {
            "问题": "下拉菜单不显示",
            "原因": "show类没有正确添加",
            "解决": "检查JavaScript的toggleClass逻辑"
        },
        {
            "问题": "移动端样式异常",
            "原因": "CSS样式优先级问题",
            "解决": "使用!important确保移动端样式生效"
        },
        {
            "问题": "多个菜单同时打开",
            "原因": "没有关闭其他菜单",
            "解决": "在打开新菜单前关闭其他菜单"
        }
    ]

    for item in issues_and_solutions:
        print(f"  ❓ {item['问题']}")
        print(f"     原因: {item['原因']}")
        print(f"     解决: {item['解决']}")
        print()

def main():
    """主测试函数"""
    print("🔍 开始测试下拉菜单功能...")
    print("=" * 60)

    test_results = []

    # 测试HTML结构
    test_results.append(test_html_dropdown_structure())

    # 测试JavaScript修复
    test_results.append(test_javascript_dropdown_fix())

    # 测试CSS样式
    test_results.append(test_css_dropdown_styles())

    # 测试Bootstrap集成
    test_results.append(test_bootstrap_integration())

    # 分析工作流程
    test_results.append(analyze_dropdown_workflow())

    # 统计结果
    print("\n" + "=" * 60)
    passed = sum(test_results)
    total = len(test_results)

    print(f"📊 测试结果: {passed}/{total} 项通过")

    if passed == total:
        print("🎉 下拉菜单功能配置正确！")

        print("\n✨ 现在应该能够:")
        print("  • 在桌面端和移动端点击菜单项展开下拉菜单")
        print("  • 看到子菜单项（如食谱库、食材管理等）")
        print("  • 点击子菜单项跳转到对应页面")
        print("  • 点击外部区域关闭下拉菜单")

        print("\n🚀 请在浏览器中测试:")
        print("  1. 刷新页面确保新的JavaScript生效")
        print("  2. 点击'菜单规划'查看是否展开子菜单")
        print("  3. 在移动端模式下测试相同功能")

        return True
    else:
        print("⚠️ 部分配置有问题，请检查相关文件。")
        generate_troubleshooting_guide()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
